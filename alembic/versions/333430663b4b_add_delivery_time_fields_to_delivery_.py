"""add_delivery_time_fields_to_delivery_orders

Revision ID: 333430663b4b
Revises: 9c1456d374c7
Create Date: 2025-10-16 16:46:41.374434

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '333430663b4b'
down_revision: Union[str, None] = '9c1456d374c7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('delivery_orders', sa.Column('delivery_start_time', sa.DateTime(), nullable=True, comment='配送开始时间'))
    op.add_column('delivery_orders', sa.Column('delivery_end_time', sa.DateTime(), nullable=True, comment='配送结束时间'))
    op.add_column('delivery_orders', sa.Column('formated_delivery_time', sa.String(length=100), nullable=True, comment='格式化的配送时间字符串'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('delivery_orders', 'formated_delivery_time')
    op.drop_column('delivery_orders', 'delivery_end_time')
    op.drop_column('delivery_orders', 'delivery_start_time')
    # ### end Alembic commands ###
