"""add_order_status_prepared_accepted_picked

Revision ID: 9c1456d374c7
Revises: aadc71a484ff
Create Date: 2025-10-14 14:49:00.520770

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9c1456d374c7'
down_revision: Union[str, None] = 'aadc71a484ff'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE orders MODIFY COLUMN STATUS ENUM('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'COMPLETED', 'CANCELLED', 'REFUNDED', 'REFUNDED_PARTIAL', 'PREPARED','PICKED', 'ACCEPTED','ARRIVED', 'ERROR') NOT NULL COMMENT '订单状态'")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "ALTER TABLE orders MODIFY COLUMN STATUS ENUM('PENDING','PAID','SHIPPED','DELIVERED','VERIFIED','COMPLETED','CANCELLED','REFUNDED','REFUNDED_PARTIAL') NOT NULL COMMENT '订单状态'")
    # ### end Alembic commands ###
