"""add_tag_rule_relation_and_delivery_order_rule_fields

Revision ID: aadc71a484ff
Revises: 902d13b1a080
Create Date: 2025-09-30 15:42:03.171483

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'aadc71a484ff'
down_revision: Union[str, None] = '902d13b1a080'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tag_rule_relations',
    sa.Column('tag_id', sa.Integer(), nullable=True),
    sa.Column('rule_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['rule_id'], ['rules.id'], name=op.f('fk_tag_rule_relations_rule_id_rules')),
    sa.ForeignKeyConstraint(['tag_id'], ['tags.id'], name=op.f('fk_tag_rule_relations_tag_id_tags')),
    sa.UniqueConstraint('tag_id', 'rule_id', name='uq_tag_rule')
    )
    op.add_column('delivery_orders', sa.Column('rule_id', sa.Integer(), nullable=True, comment='关联的时间规则ID'))
    op.add_column('delivery_orders', sa.Column('rule_item_id', sa.Integer(), nullable=True, comment='关联的时间规则项ID'))
    op.create_foreign_key(op.f('fk_delivery_orders_rule_id_rules'), 'delivery_orders', 'rules', ['rule_id'], ['id'])
    op.create_foreign_key(op.f('fk_delivery_orders_rule_item_id_rule_items'), 'delivery_orders', 'rule_items', ['rule_item_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('fk_delivery_orders_rule_item_id_rule_items'), 'delivery_orders', type_='foreignkey')
    op.drop_constraint(op.f('fk_delivery_orders_rule_id_rules'), 'delivery_orders', type_='foreignkey')
    op.drop_column('delivery_orders', 'rule_item_id')
    op.drop_column('delivery_orders', 'rule_id')
    op.drop_table('tag_rule_relations')
    # ### end Alembic commands ###
