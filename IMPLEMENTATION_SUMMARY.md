# Tag Service DINING_RESERVATION 时间生成功能实现总结

## 概述

本次实现为 `app/service/tag.py` 中的 `get_rules_with_items_by_tag_name` 方法添加了对 `DINING_RESERVATION` 类型规则的时间自动生成功能。

## 修改的文件

### 1. `app/service/tag.py`

**主要修改：**

#### 1.1 导入新模块
```python
from app.models.rule import Rule, RuleItem, RuleType, DiningReservationRule
```

#### 1.2 增强 `get_rules_with_items_by_tag_name` 方法

在原有逻辑基础上添加：

- 检测规则类型是否为 `DINING_RESERVATION`
- 如果是，则获取 `dining_reservation_rules` 表的详细信息
- 调用 `_generate_rule_times` 生成规则级别的时间信息，添加到 `rule_times` 字段
- 调用 `_generate_rule_item_times` 生成规则项级别的时间信息，添加到 `rule_items_times` 字段

#### 1.3 新增辅助方法

**`_generate_rule_times(dining_rule: DiningReservationRule) -> List[Dict[str, str]]`**

功能：根据 DiningReservationRule 生成时间列表

处理逻辑：
1. 从 `dining_start_time_cron_str` 和 `dining_end_time_cron_str` 生成就餐时间
2. 从 `verify_start_time_cron_str` 和 `verify_end_time_cron_str` 生成核销时间
3. 根据 `order_deadline`（分钟）计算订单截止时间 = dining_start_time - order_deadline
4. 根据 `cancellation_deadline`（分钟）计算取消截止时间 = dining_start_time - cancellation_deadline
5. 生成数量由 `generated_count` 决定，默认为 5

返回格式：
```python
[
    {
        "dining_start_time": "2025-10-01 11:30",
        "dining_end_time": "2025-10-01 14:00",
        "verify_start_time": "2025-10-01 11:00",
        "verify_end_time": "2025-10-01 14:30",
        "order_deadline": "2025-09-30 21:00",
        "cancel_deadline": "2025-10-01 09:00"
    },
    ...
]
```

**`_generate_rule_item_times(rule_item: RuleItem) -> List[Dict[str, str]]`**

功能：根据 RuleItem 生成时间列表

处理逻辑：
1. 从 `start_time_cron_str` 和 `end_time_cron_str` 生成就餐时间
2. 根据 `order_deadline`（分钟）计算订单截止时间 = start_time - order_deadline
3. 根据 `cancellation_deadline`（分钟）计算取消截止时间 = start_time - cancellation_deadline
4. 生成数量由 `generated_count` 决定，默认为 5

返回格式：
```python
[
    {
        "dining_start_time": "2025-10-01 11:30",
        "dining_end_time": "2025-10-01 11:45",
        "order_deadline": "2025-09-30 21:00",
        "cancel_deadline": "2025-10-01 09:00"
    },
    ...
]
```

**`_get_next_times_from_cron(cron_str: str, start_time: datetime, count: int) -> List[datetime]`**

功能：从 cron 表达式生成指定数量的时间点

使用 `croniter` 库解析 cron 表达式并生成时间序列。

## 数据结构变化

### 原有返回结构
```json
{
  "id": 7,
  "name": "商务餐午餐规则",
  "type": "dining_reservation",
  "rule_items": [...]
}
```

### 新增返回结构（仅对 DINING_RESERVATION 类型）
```json
{
  "id": 7,
  "name": "商务餐午餐规则",
  "type": "dining_reservation",
  "rule_times": [
    {
      "dining_start_time": "2025-10-01 11:30",
      "dining_end_time": "2025-10-01 14:00",
      "verify_start_time": "2025-10-01 11:00",
      "verify_end_time": "2025-10-01 14:30",
      "order_deadline": "2025-09-30 21:00",
      "cancel_deadline": "2025-10-01 09:00"
    }
  ],
  "rule_items": [
    {
      "id": 17,
      "name": "（时段1）11:30-11:45",
      "rule_items_times": [
        {
          "dining_start_time": "2025-10-01 11:30",
          "dining_end_time": "2025-10-01 11:45",
          "order_deadline": "2025-09-30 21:00",
          "cancel_deadline": "2025-10-01 09:00"
        }
      ]
    }
  ]
}
```

## 关键计算逻辑

### 订单截止时间计算
```python
order_deadline_time = dining_start_time - timedelta(minutes=order_deadline)
```

**示例：**
- 就餐开始时间：2025-10-01 11:30
- 订单截止提前量：870 分钟（14.5 小时）
- **订单截止时间：2025-09-30 21:00**

### 取消截止时间计算
```python
cancel_deadline_time = dining_start_time - timedelta(minutes=cancellation_deadline)
```

**示例：**
- 就餐开始时间：2025-10-01 11:30
- 取消截止提前量：150 分钟（2.5 小时）
- **取消截止时间：2025-10-01 09:00**

## 新增文件

### 1. `test_tag_service_dining_reservation.py`
测试脚本，用于验证功能实现。

### 2. `doc/tag_service_dining_reservation_times.md`
详细的功能文档，包含使用说明和示例。

### 3. `doc/tag_service_api_response_example.json`
完整的 API 返回示例，展示实际数据格式。

### 4. `IMPLEMENTATION_SUMMARY.md`
本文件，实现总结文档。

## 使用方法

```python
from app.db.session import SessionLocal
from app.service.tag import tag_service

db = SessionLocal()

# 获取标签关联的规则及其时间信息
result = tag_service.get_rules_with_items_by_tag_name(db, "商务餐")

for rule in result:
    # 对于 DINING_RESERVATION 类型，会包含 rule_times
    if 'rule_times' in rule:
        for time_info in rule['rule_times']:
            print(f"就餐时间: {time_info['dining_start_time']} - {time_info['dining_end_time']}")
            print(f"订单截止: {time_info['order_deadline']}")
    
    # 规则项也会包含 rule_items_times
    for item in rule['rule_items']:
        if 'rule_items_times' in item:
            for time_info in item['rule_items_times']:
                print(f"时间段: {time_info['dining_start_time']} - {time_info['dining_end_time']}")

db.close()
```

## 测试

运行测试脚本：
```bash
cd /Users/<USER>/Project/yihes/yh-vegan-admin
poetry run python test_tag_service_dining_reservation.py
```

## 兼容性说明

1. **向后兼容**：对于非 `DINING_RESERVATION` 类型的规则，返回结构保持不变
2. **可选字段**：`rule_times` 和 `rule_items_times` 仅在规则类型为 `DINING_RESERVATION` 时出现
3. **异常处理**：如果 cron 表达式解析失败，返回空列表，不影响其他数据

## 注意事项

1. **Cron 表达式**：必须确保 cron 表达式格式正确
2. **时间格式**：所有时间统一使用 `YYYY-MM-DD HH:MM` 格式
3. **默认值**：如果 `generated_count` 为 None，默认生成 5 个时间段
4. **数据库查询**：对于 DINING_RESERVATION 类型会额外查询 `dining_reservation_rules` 表

## 依赖

- `croniter`: 用于解析 cron 表达式
- `datetime`: 用于时间计算
- `timedelta`: 用于时间偏移计算

## 相关模型

- `Rule`: 基础规则模型
- `DiningReservationRule`: 餐厅预订规则模型（继承自 Rule）
- `RuleItem`: 规则项模型
- `RuleType`: 规则类型枚举

## API 返回格式

符合项目标准的 API 返回格式：
```json
{
  "code": 200,
  "message": "success",
  "data": [...]
}
```

完整示例请参考：`doc/tag_service_api_response_example.json`

## 更新日志

- **2025-09-30**: 初始实现
  - 添加 `rule_times` 字段生成
  - 添加 `rule_items_times` 字段生成
  - 实现订单截止时间和取消截止时间计算
  - 添加测试脚本和文档

