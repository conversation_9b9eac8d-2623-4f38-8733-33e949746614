#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
外卖订单统计通知测试脚本

用于测试外卖订单统计通知功能
"""

import asyncio
import sys
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, '/Users/<USER>/Project/yihes/yh-vegan-admin')

from app.db.session import SessionLocal
from app.tasks.notifications.delivery import (
    get_delivery_order_statistics,
    classify_delivery_statistics_by_time,
    send_daily_delivery_statistic_notification
)
from app.utils.logger import logger


def test_get_statistics():
    """测试获取外卖订单统计数据"""
    print("\n" + "="*80)
    print("测试1: 获取外卖订单统计数据")
    print("="*80)
    
    db = SessionLocal()
    try:
        # 测试今天的数据
        today = datetime.now().date()
        start_time = datetime.combine(today, datetime.min.time())
        end_time = datetime.combine(today, datetime.max.time())
        
        print(f"\n查询时间范围: {start_time} 至 {end_time}")
        
        order_count, total_amount, total_delivery_fee, total_paid_amount = get_delivery_order_statistics(
            db, start_time, end_time
        )
        
        print(f"\n统计结果:")
        print(f"  订单数量: {order_count}")
        print(f"  订单总金额: {total_amount:.2f} 元")
        print(f"  配送费总额: {total_delivery_fee:.2f} 元")
        print(f"  实际支付总额: {total_paid_amount:.2f} 元")
        
        return True
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def test_classify_statistics():
    """测试分类统计外卖订单数据"""
    print("\n" + "="*80)
    print("测试2: 分类统计外卖订单数据")
    print("="*80)
    
    db = SessionLocal()
    try:
        # 测试今天的数据
        today = datetime.now().date()
        start_time = datetime.combine(today, datetime.min.time())
        end_time = datetime.combine(today, datetime.max.time())
        
        print(f"\n查询时间范围: {start_time} 至 {end_time}")
        
        statistics = classify_delivery_statistics_by_time(db, start_time, end_time)
        
        print(f"\n分类统计结果:")
        print(f"\n午餐订单:")
        print(f"  订单数: {statistics['lunch']['count']} 单")
        print(f"  订单金额: {statistics['lunch']['total_amount']:.2f} 元")
        print(f"  配送费: {statistics['lunch']['delivery_fee']:.2f} 元")
        print(f"  实际支付: {statistics['lunch']['paid_amount']:.2f} 元")
        
        print(f"\n晚餐订单:")
        print(f"  订单数: {statistics['dinner']['count']} 单")
        print(f"  订单金额: {statistics['dinner']['total_amount']:.2f} 元")
        print(f"  配送费: {statistics['dinner']['delivery_fee']:.2f} 元")
        print(f"  实际支付: {statistics['dinner']['paid_amount']:.2f} 元")
        
        return True
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


async def test_send_notification():
    """测试发送通知"""
    print("\n" + "="*80)
    print("测试3: 发送外卖订单统计通知")
    print("="*80)
    
    try:
        print("\n发送今日午餐订单统计通知...")
        await send_daily_delivery_statistic_notification("lunch", "today")
        print("✅ 今日午餐订单统计通知发送完成")
        
        # 等待一下，避免发送太快
        await asyncio.sleep(2)
        
        print("\n发送今日晚餐订单统计通知...")
        await send_daily_delivery_statistic_notification("dinner", "today")
        print("✅ 今日晚餐订单统计通知发送完成")
        
        return True
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("\n" + "="*80)
    print("外卖订单统计通知功能测试")
    print("="*80)
    print(f"测试时间: {datetime.now()}")
    
    results = []
    
    # 测试1: 获取统计数据
    results.append(("获取统计数据", test_get_statistics()))
    
    # 测试2: 分类统计
    results.append(("分类统计", test_classify_statistics()))
    
    # 测试3: 发送通知
    print("\n是否要测试发送通知? (y/n): ", end="")
    choice = input().strip().lower()
    if choice == 'y':
        result = asyncio.run(test_send_notification())
        results.append(("发送通知", result))
    else:
        print("跳过发送通知测试")
    
    # 输出测试结果
    print("\n" + "="*80)
    print("测试结果汇总")
    print("="*80)
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
    
    # 判断是否全部通过
    all_passed = all(result for _, result in results)
    if all_passed:
        print("\n🎉 所有测试通过!")
    else:
        print("\n⚠️  部分测试失败，请检查日志")


if __name__ == "__main__":
    main()

