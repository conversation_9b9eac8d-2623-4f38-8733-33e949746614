/* 外卖管理页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 权限检查 */
.permission-check {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.permission-message {
  text-align: center;
}

.permission-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}

.permission-text {
  font-size: 32rpx;
  color: #666;
}

/* 主要内容 */
.main-content {
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 日期筛选区域 */
.date-filter {
  padding: 30rpx;
  background-color: white;
  border-bottom: 1rpx solid #eee;
}

.date-filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* 快捷日期选择 */
.quick-dates {
  display: flex;
  gap: 15rpx;
  margin-bottom: 30rpx;
  flex-wrap: wrap;
}

.quick-date-btn {
  flex: 1;
  min-width: 150rpx;
  padding: 20rpx 15rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background-color: white;
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.2;
}

.quick-date-btn::after {
  border: none;
}

.quick-date-btn:active {
  border-color: #1989fa;
  background-color: #f0f8ff;
  color: #1989fa;
}

.quick-date-btn.active {
  border-color: #1989fa;
  background-color: #1989fa;
  color: white;
}

/* 自定义日期选择 */
.custom-dates {
  margin-top: 20rpx;
}

.date-picker-group {
  display: flex;
  gap: 20rpx;
}

.date-picker-item {
  flex: 1;
}

.date-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background-color: white;
}

.date-value {
  font-size: 28rpx;
  color: #333;
}

.date-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 统计概览 */
.statistics-overview {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  margin: 30rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 60rpx;
  background-color: #ddd;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #1989fa;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 数据列表 */
.data-list {
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
  margin: 30rpx;
}

.list-header {
  padding: 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.pagination-info {
  font-size: 24rpx;
  color: #666;
}

/* 批量操作区域 */
.batch-operations {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #eee;
}

.batch-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.selected-count {
  font-size: 24rpx;
  color: #666;
}

.batch-right {
  display: flex;
  gap: 15rpx;
}

.batch-print-btn,
.date-print-btn {
  padding: 12rpx 20rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  border: none;
}

.batch-print-btn {
  background-color: #1989fa;
  color: white;
}

.batch-print-btn.disabled {
  background-color: #ccc;
  color: #999;
}

.batch-print-btn::after,
.date-print-btn::after {
  border: none;
}

.date-print-btn {
  background-color: #52c41a;
  color: white;
}

.loading,
.empty {
  padding: 80rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.list-content {
  padding: 0;
}

.list-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-item:last-child {
  border-bottom: none;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.item-header-left {
  display: flex;
  align-items: center;
  gap: 15rpx;
  flex: 1;
}

.item-header-right {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.item-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.item-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.status-paid {
  background-color: #52c41a;
}

.status-completed {
  background-color: #52c41a;
}

.status-pending {
  background-color: #faad14;
}

.status-prepared {
  background-color: #722ed1;
}

.status-accepted {
  background-color: #13c2c2;
}

.status-arrived {
  background-color: #13c2c2;
}

.status-picked {
  background-color: #1890ff;
}

.status-delivering {
  background-color: #1890ff;
}

.status-delivered {
  background-color: #52c41a;
}

.status-cancelled {
  background-color: #ff4d4f;
}

/* 操作按钮行 */
.action-buttons-row {
  display: flex;
  gap: 15rpx;
  margin-top: 20rpx;
  padding-top: 15rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  flex: 1;
  padding: 12rpx 20rpx;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: white;
  text-align: center;
}

.action-btn::after {
  border: none;
}

/* 订单查询按钮 */
.order-btn {
  background-color: #52c41a;
}

.order-btn:active {
  background-color: #389e0d;
}

/* 骑手查询按钮 */
.rider-btn {
  background-color: #faad14;
}

.rider-btn:active {
  background-color: #d48806;
}

/* 打印按钮 */
.print-btn {
  background-color: #1989fa;
}

.print-btn:active {
  background-color: #0c7cd5;
}

.item-details {
  margin-top: 20rpx;
}

.detail-row {
  display: flex;
  margin-bottom: 10rpx;
  align-items: flex-start;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.payment-status-paid {
  color: #52c41a;
  font-weight: bold;
}

.payment-status-pending {
  color: #faad14;
  font-weight: bold;
}

.payment-status-refunded {
  color: #ff4d4f;
  font-weight: bold;
}

.total-amount {
  color: #1989fa;
  font-weight: bold;
  font-size: 28rpx;
}

/* 订单明细 */
.order-items {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.order-items-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item-name {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

.order-item-quantity {
  font-size: 24rpx;
  color: #666;
  margin: 0 20rpx;
}

.order-item-price {
  font-size: 24rpx;
  color: #1989fa;
  font-weight: bold;
}

/* 分页控制 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx;
  background-color: #f8f9fa;
  gap: 20rpx;
}

.pagination-btn {
  padding: 15rpx 30rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 6rpx;
  background-color: white;
  font-size: 28rpx;
  color: #333;
}

.pagination-btn::after {
  border: none;
}

.pagination-btn:not(.disabled):active {
  background-color: #f0f8ff;
  border-color: #1989fa;
  color: #1989fa;
}

.pagination-btn.disabled {
  background-color: #f5f5f5;
  color: #ccc;
  border-color: #e0e0e0;
}

.pagination-text {
  font-size: 28rpx;
  color: #666;
  margin: 0 20rpx;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .date-picker-group {
    flex-direction: column;
    gap: 20rpx;
  }

  .statistics-overview {
    flex-direction: column;
    gap: 20rpx;
  }

  .stat-item:not(:last-child)::after {
    display: none;
  }

  .quick-dates {
    gap: 10rpx;
  }

  .quick-date-btn {
    font-size: 24rpx;
    padding: 12rpx 8rpx;
    min-width: 120rpx;
  }

  .list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10rpx;
  }

  .pagination {
    flex-direction: column;
    gap: 15rpx;
  }
}
