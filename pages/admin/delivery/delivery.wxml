<view class="container">
  <!-- 权限检查 -->
  <view wx:if="{{!isSystemAdmin}}" class="permission-check">
    <view class="permission-message">
      <text class="permission-icon">🔒</text>
      <text class="permission-text">权限检查中...</text>
    </view>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 日期筛选区域 -->
    <view class="date-filter">
      <view class="date-filter-title">筛选日期</view>
      
      <!-- 快捷日期选择 -->
      <view class="quick-dates">
        <button 
          wx:for="{{quickDates}}" 
          wx:key="value"
          class="quick-date-btn {{activeQuickDate === item.value ? 'active' : ''}}"
          data-value="{{item.value}}"
          bindtap="onQuickDateSelect"
        >
          {{item.label}}
        </button>
      </view>
      
      <!-- 自定义日期选择 -->
      <view class="custom-dates">
        <view class="date-picker-group">
          <view class="date-picker-item">
            <text class="date-label">开始日期</text>
            <picker mode="date" value="{{startDate}}" bindchange="onStartDateChange">
              <view class="date-picker">
                <text class="date-value">{{startDate || '请选择'}}</text>
                <text class="date-arrow">▼</text>
              </view>
            </picker>
          </view>
          
          <view class="date-picker-item">
            <text class="date-label">结束日期</text>
            <picker mode="date" value="{{endDate}}" bindchange="onEndDateChange">
              <view class="date-picker">
                <text class="date-value">{{endDate || '请选择'}}</text>
                <text class="date-arrow">▼</text>
              </view>
            </picker>
          </view>
        </view>
      </view>
    </view>

    <!-- 外卖订单统计概览 -->
    <view class="statistics-overview">
      <view class="stat-item">
        <text class="stat-number">{{deliveryData.total}}</text>
        <text class="stat-label">订单总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{deliveryData.completedCount}}</text>
        <text class="stat-label">已完成</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">¥{{deliveryData.totalAmount}}</text>
        <text class="stat-label">总金额</text>
      </view>
    </view>

    <!-- 外卖订单列表 -->
    <view class="data-list">
      <view class="list-header">
        <text class="list-title">外卖订单 ({{deliveryData.list.length}}条)</text>
        <view class="pagination-info">
          <text>第{{currentPage}}页，共{{totalPages}}页</text>
        </view>
      </view>

      <!-- 批量操作区域 -->
      <view wx:if="{{deliveryData.list.length > 0}}" class="batch-operations">
        <view class="batch-left">
          <checkbox-group bindchange="onSelectAllChange">
            <checkbox value="all" checked="{{isAllSelected}}">全选</checkbox>
          </checkbox-group>
          <!-- <text class="selected-count">已选择 {{selectedOrders.length}} 项</text>-->
        </view>
        <view class="batch-right">
          <button
            class="batch-print-btn {{selectedOrders.length === 0 ? 'disabled' : ''}}"
            bindtap="batchPrint"
            disabled="{{selectedOrders.length === 0}}"
          >
            批量打印 ({{selectedOrders.length}})
          </button>
          <!-- 
          <button class="date-print-btn" bindtap="printByDate">
            打印当前日期
          </button>
          -->
        </view>
      </view>

      <view wx:if="{{deliveryData.loading}}" class="loading">
        <text>加载中...</text>
      </view>

      <view wx:elif="{{deliveryData.list.length === 0}}" class="empty">
        <text>暂无数据</text>
      </view>

      <view wx:else class="list-content">
        <view
          wx:for="{{deliveryData.list}}"
          wx:key="id"
          class="list-item"
        >
          <view class="item-header">
            <view class="item-header-left">
              <checkbox-group bindchange="onOrderSelect" data-order-id="{{item.id}}">
                <checkbox value="{{item.id}}" checked="{{item.selected}}"></checkbox>
              </checkbox-group>
              <text class="item-title">订单 #{{item.order_no}}</text>
            </view>
            <view class="item-header-right">
              <text class="item-status status-{{item.status}}">
                <text wx:if="{{item.status === 'paid'}}">已支付</text>
                <text wx:elif="{{item.status === 'completed'}}">已完成</text>
                <text wx:elif="{{item.status === 'pending'}}">待处理</text>
                <text wx:elif="{{item.status === 'cancelled'}}">已取消</text>
                <text wx:elif="{{item.status === 'prepared'}}">已备餐</text>
                <text wx:elif="{{item.status === 'accepted'}}">已接单</text>
                <text wx:elif="{{item.status === 'arrived'}}">已到店</text>
                <text wx:elif="{{item.status === 'picked'}}">配送中</text>
                <text wx:elif="{{item.status === 'delivering'}}">配送中</text>
                <text wx:elif="{{item.status === 'delivered'}}">已送达</text>
                <text wx:else>{{item.status}}</text>
              </text>
            </view>
          </view>

          <!-- 操作按钮行 -->
          <view class="action-buttons-row">
            <button class="action-btn order-btn" bindtap="queryOrderDetail" data-order-no="{{item.order_no}}" data-fengniao-id="{{item.fengniao_order_id}}">
              订单
            </button>
            <button class="action-btn rider-btn" bindtap="queryRiderInfo" data-order-no="{{item.order_no}}" data-fengniao-id="{{item.fengniao_order_id}}">
              骑手
            </button>
            <button class="action-btn print-btn" bindtap="printSingleOrder" data-order-id="{{item.id}}">
              打印
            </button>
          </view>
          
          <view class="item-details">
            <view class="detail-row">
              <text class="detail-label">用户姓名：</text>
              <text class="detail-value">{{item.user_name}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">联系电话：</text>
              <text class="detail-value">{{item.user_phone}}</text>
            </view>
            <view wx:if="{{item.fengniao_order_id}}" class="detail-row">
              <text class="detail-label">蜂鸟订单号：</text>
              <text class="detail-value">{{item.fengniao_order_id}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">配送地址：</text>
              <text class="detail-value">{{item.delivery_address}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">配送时间：</text>
              <text class="detail-value">{{item.delivery_time_raw.label || item.delivery_time || '待安排'}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">支付状态：</text>
              <text class="detail-value payment-status-{{item.payment_status}}">
                <text wx:if="{{item.payment_status === 'paid'}}">已支付</text>
                <text wx:elif="{{item.payment_status === 'pending'}}">待支付</text>
                <text wx:elif="{{item.payment_status === 'refunded'}}">已退款</text>
                <text wx:else>{{item.payment_status}}</text>
              </text>
            </view>
            <view class="detail-row">
              <text class="detail-label">支付方式：</text>
              <text class="detail-value">
                <text wx:if="{{item.payment_method === 'account_balance'}}">账户余额</text>
                <text wx:elif="{{item.payment_method === 'wechat'}}">微信支付</text>
                <text wx:elif="{{item.payment_method === 'alipay'}}">支付宝</text>
                <text wx:else>{{item.payment_method}}</text>
              </text>
            </view>
            <view class="detail-row">
              <text class="detail-label">配送费：</text>
              <text class="detail-value">¥{{item.delivery_fee}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">商品金额：</text>
              <text class="detail-value">¥{{item.payable_amount}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">订单总额：</text>
              <text class="detail-value total-amount">¥{{item.total_amount}}</text>
            </view>

            <!-- 订单明细 -->
            <view wx:if="{{item.items && item.items.length > 0}}" class="order-items">
              <text class="order-items-title">订单明细：</text>
              <view
                wx:for="{{item.items}}"
                wx:key="id"
                wx:for-item="orderItem"
                class="order-item"
              >
                <text class="order-item-name">{{orderItem.product_name}}</text>
                <text class="order-item-quantity">×{{orderItem.quantity}}</text>
                <text class="order-item-price">¥{{orderItem.payable_amount}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 分页控制 -->
      <view wx:if="{{totalPages > 1}}" class="pagination">
        <button 
          class="pagination-btn {{currentPage === 1 ? 'disabled' : ''}}" 
          bindtap="prevPage"
          disabled="{{currentPage === 1}}"
        >
          上一页
        </button>
        <text class="pagination-text">{{currentPage}} / {{totalPages}}</text>
        <button 
          class="pagination-btn {{currentPage === totalPages ? 'disabled' : ''}}" 
          bindtap="nextPage"
          disabled="{{currentPage === totalPages}}"
        >
          下一页
        </button>
      </view>
    </view>
  </view>
</view>
