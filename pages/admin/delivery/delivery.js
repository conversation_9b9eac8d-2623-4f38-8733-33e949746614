// 外卖管理页面
const app = getApp();

Page({
  data: {
    isSystemAdmin: false,
    
    // 日期筛选
    startDate: '',
    endDate: '',
    activeQuickDate: 'today', // 当前选中的快捷日期
    
    // 快捷日期选择
    quickDates: [
      { label: '今天', value: 'today' },
      { label: '明天', value: 'tomorrow' },
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' }
    ],
    
    // 外卖订单数据
    deliveryData: {
      list: [],
      total: 0,
      completedCount: 0,
      totalAmount: 0,
      loading: false
    },
    
    // 分页数据
    currentPage: 1,
    pageSize: 10,
    totalPages: 1,

    // 选择和打印相关
    selectedOrders: [], // 选中的订单ID列表
    isAllSelected: false, // 是否全选
    isPrinting: false // 是否正在打印
  },

  onLoad: function(options) {
    wx.setNavigationBarTitle({
      title: '外卖管理'
    });
    
    this.checkSystemAdmin();
    this.initDates();
  },

  onShow: function() {
    this.checkSystemAdmin();
  },

  // 检查系统管理员权限
  checkSystemAdmin: function() {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    wx.request({
      url: app.globalData.baseUrl + '/admin/is_system_admin',
      method: 'GET',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('权限检查响应:', res.data);
        if (res.data.status === 200 && res.data.data) {
          console.log('权限检查通过，设置isSystemAdmin为true');
          this.setData({
            isSystemAdmin: true
          });
          console.log('准备调用loadData');
          this.loadData();
        } else {
          wx.showToast({
            title: '无权限访问',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      },
      fail: (err) => {
        console.error('检查系统管理员权限失败', err);
        wx.showToast({
          title: '权限检查失败',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  // 初始化日期
  initDates: function() {
    const today = new Date();
    const todayStr = this.formatDate(today);
    
    this.setData({
      startDate: todayStr,
      endDate: todayStr
    });
  },

  // 格式化日期
  formatDate: function(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const monthStr = month < 10 ? '0' + month : '' + month;
    const dayStr = day < 10 ? '0' + day : '' + day;
    return year + '-' + monthStr + '-' + dayStr;
  },

  // 开始日期选择
  onStartDateChange: function(e) {
    this.setData({
      startDate: e.detail.value,
      activeQuickDate: '', // 清除快捷日期选中状态
      currentPage: 1 // 重置到第一页
    });
    this.loadData();
  },

  // 结束日期选择
  onEndDateChange: function(e) {
    this.setData({
      endDate: e.detail.value,
      activeQuickDate: '', // 清除快捷日期选中状态
      currentPage: 1 // 重置到第一页
    });
    this.loadData();
  },

  // 快捷日期选择
  onQuickDateSelect: function(e) {
    const value = e.currentTarget.dataset.value;
    const today = new Date();
    let startDate, endDate;

    if (value === 'today') {
      startDate = endDate = this.formatDate(today);
    } else if (value === 'tomorrow') {
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);
      startDate = endDate = this.formatDate(tomorrow);
    } else if (value === 'week') {
      // 本周：周一到周日
      const dayOfWeek = today.getDay();
      const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
      const monday = new Date(today);
      monday.setDate(today.getDate() + mondayOffset);
      const sunday = new Date(monday);
      sunday.setDate(monday.getDate() + 6);
      
      startDate = this.formatDate(monday);
      endDate = this.formatDate(sunday);
    } else if (value === 'month') {
      // 本月：本月第一天到最后一天
      const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
      const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      
      startDate = this.formatDate(firstDay);
      endDate = this.formatDate(lastDay);
    }

    this.setData({
      startDate: startDate,
      endDate: endDate,
      activeQuickDate: value,
      currentPage: 1 // 重置到第一页
    });
    this.loadData();
  },

  // 加载外卖订单数据
  loadData: function() {
    const token = wx.getStorageSync('token');
    if (!token) return;

    this.setData({
      'deliveryData.loading': true
    });

    console.log('加载外卖订单数据，页码:', this.data.currentPage);
    console.log('日期范围:', this.data.startDate, '到', this.data.endDate);

    wx.request({
      url: app.globalData.baseUrl + '/admin/delivery/delivery_orders',
      method: 'GET',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      data: {
        page: this.data.currentPage,
        page_size: this.data.pageSize,
        start_date: this.data.startDate,
        end_date: this.data.endDate
      },
      success: (res) => {
        console.log('外卖订单API响应:', res.data);
        if (res.data.code === 200) {
          const data = res.data.data;
          const list = data.list || [];
          
          // 计算统计数据
          let completedCount = 0;
          let totalAmount = 0;
          
          list.forEach(item => {
            if (item.status === 'completed') {
              completedCount++;
            }
            totalAmount += parseFloat(item.total_amount || 0);
          });

          // 计算总页数
          const totalPages = Math.ceil(data.total / this.data.pageSize);

          // 为每个订单添加选中状态
          const listWithSelection = list.map(item => ({
            ...item,
            selected: false
          }));

          this.setData({
            'deliveryData.list': listWithSelection,
            'deliveryData.total': data.total || 0,
            'deliveryData.completedCount': completedCount,
            'deliveryData.totalAmount': totalAmount.toFixed(2),
            'deliveryData.loading': false,
            totalPages: totalPages,
            selectedOrders: [], // 清除选择状态
            isAllSelected: false
          });
        } else {
          console.log('API返回错误:', res.data);
          wx.showToast({
            title: res.data.message || '获取数据失败',
            icon: 'none'
          });
          this.setData({
            'deliveryData.loading': false
          });
        }
      },
      fail: (err) => {
        console.error('获取外卖订单数据失败', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({
          'deliveryData.loading': false
        });
      }
    });
  },

  // 上一页
  prevPage: function() {
    if (this.data.currentPage > 1) {
      this.setData({
        currentPage: this.data.currentPage - 1
      });
      this.loadData();
    }
  },

  // 下一页
  nextPage: function() {
    if (this.data.currentPage < this.data.totalPages) {
      this.setData({
        currentPage: this.data.currentPage + 1
      });
      this.loadData();
    }
  },

  // 全选/取消全选
  onSelectAllChange: function(e) {
    const values = e.detail.value;
    const isSelectAll = values.includes('all');
    const list = this.data.deliveryData.list;

    // 更新每个订单的选中状态
    const updatedList = list.map(item => ({
      ...item,
      selected: isSelectAll
    }));

    // 更新选中的订单ID列表
    const selectedOrders = isSelectAll ? list.map(item => item.id) : [];

    this.setData({
      'deliveryData.list': updatedList,
      selectedOrders: selectedOrders,
      isAllSelected: isSelectAll
    });
  },

  // 单个订单选择
  onOrderSelect: function(e) {
    const orderId = parseInt(e.currentTarget.dataset.orderId);
    const values = e.detail.value;
    const isSelected = values.includes(orderId.toString());

    // 更新订单列表中的选中状态
    const list = this.data.deliveryData.list;
    const updatedList = list.map(item => {
      if (item.id === orderId) {
        return { ...item, selected: isSelected };
      }
      return item;
    });

    // 更新选中的订单ID列表
    let selectedOrders = [...this.data.selectedOrders];
    if (isSelected) {
      if (!selectedOrders.includes(orderId)) {
        selectedOrders.push(orderId);
      }
    } else {
      selectedOrders = selectedOrders.filter(id => id !== orderId);
    }

    // 检查是否全选
    const isAllSelected = selectedOrders.length === list.length && list.length > 0;

    this.setData({
      'deliveryData.list': updatedList,
      selectedOrders: selectedOrders,
      isAllSelected: isAllSelected
    });
  },

  // 打印单个订单
  printSingleOrder: function(e) {
    const orderId = parseInt(e.currentTarget.dataset.orderId);
    this.callPrintAPI({ order_id: orderId });
  },

  // 批量打印选中订单
  batchPrint: function() {
    if (this.data.selectedOrders.length === 0) {
      wx.showToast({
        title: '请先选择要打印的订单',
        icon: 'none'
      });
      return;
    }

    this.callPrintAPI({ order_ids: this.data.selectedOrders });
  },

  // 按日期打印
  printByDate: function() {
    this.callPrintAPI({
      start_date: this.data.startDate,
      end_date: this.data.endDate
    });
  },

  // 调用打印API
  callPrintAPI: function(params) {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    if (this.data.isPrinting) {
      wx.showToast({
        title: '正在打印中，请稍候',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isPrinting: true
    });

    wx.showLoading({
      title: '正在打印...'
    });

    console.log('打印参数:', params);

    wx.request({
      url: app.globalData.baseUrl + '/admin/delivery/print_delivery_orders',
      method: 'POST',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      data: params,
      success: (res) => {
        console.log('打印API响应:', res.data);
        wx.hideLoading();

        if (res.data.code === 200) {
          const data = res.data.data;
          wx.showModal({
            title: '打印结果',
            content: `${res.data.message}\n成功打印: ${data.printed_count}/${data.total_count} 个订单`,
            showCancel: false,
            confirmText: '确定'
          });

          // 如果是批量打印，清除选择状态
          if (params.order_ids) {
            this.clearSelection();
          }
        } else {
          wx.showModal({
            title: '打印失败',
            content: res.data.message || '打印失败，请重试',
            showCancel: false,
            confirmText: '确定'
          });
        }
      },
      fail: (err) => {
        console.error('打印API调用失败', err);
        wx.hideLoading();
        wx.showModal({
          title: '打印失败',
          content: '网络错误，请检查网络连接后重试',
          showCancel: false,
          confirmText: '确定'
        });
      },
      complete: () => {
        this.setData({
          isPrinting: false
        });
      }
    });
  },

  // 清除选择状态
  clearSelection: function() {
    const list = this.data.deliveryData.list;
    const updatedList = list.map(item => ({
      ...item,
      selected: false
    }));

    this.setData({
      'deliveryData.list': updatedList,
      selectedOrders: [],
      isAllSelected: false
    });
  },

  // 查询订单详情
  queryOrderDetail: function(e) {
    const orderNo = e.currentTarget.dataset.orderNo;
    const fengniaoId = e.currentTarget.dataset.fengniaoId;

    if (!orderNo && !fengniaoId) {
      wx.showToast({
        title: '订单信息不完整',
        icon: 'none'
      });
      return;
    }

    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '查询中...'
    });

    // 构建查询参数
    let params = {};
    if (orderNo) {
      params.partner_order_code = orderNo;
    }
    if (fengniaoId) {
      params.order_id = fengniaoId;
    }

    wx.request({
      url: app.globalData.baseUrl + '/admin/delivery/order_detail',
      method: 'GET',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      data: params,
      success: (res) => {
        wx.hideLoading();
        console.log('订单详情API响应:', res.data);

        if (res.data.code === 200) {
          const data = res.data.data;
          this.showOrderDetailModal(data);
        } else {
          wx.showModal({
            title: '查询失败',
            content: res.data.message || '获取订单详情失败',
            showCancel: false,
            confirmText: '确定'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('查询订单详情失败', err);
        wx.showModal({
          title: '查询失败',
          content: '网络错误，请检查网络连接后重试',
          showCancel: false,
          confirmText: '确定'
        });
      }
    });
  },

  // 查询骑手信息
  queryRiderInfo: function(e) {
    const orderNo = e.currentTarget.dataset.orderNo;
    const fengniaoId = e.currentTarget.dataset.fengniaoId;

    if (!orderNo && !fengniaoId) {
      wx.showToast({
        title: '订单信息不完整',
        icon: 'none'
      });
      return;
    }

    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '查询中...'
    });

    // 构建查询参数
    let params = {};
    if (orderNo) {
      params.partner_order_code = orderNo;
    }
    if (fengniaoId) {
      params.order_id = fengniaoId;
    }

    wx.request({
      url: app.globalData.baseUrl + '/admin/delivery/rider_info',
      method: 'GET',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      data: params,
      success: (res) => {
        wx.hideLoading();
        console.log('骑手信息API响应:', res.data);

        if (res.data.code === 200) {
          const data = res.data.data;
          this.showRiderInfoModal(data);
        } else {
          wx.showModal({
            title: '查询失败',
            content: res.data.message || '获取骑手信息失败',
            showCancel: false,
            confirmText: '确定'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('查询骑手信息失败', err);
        wx.showModal({
          title: '查询失败',
          content: '网络错误，请检查网络连接后重试',
          showCancel: false,
          confirmText: '确定'
        });
      }
    });
  },

  // 显示订单详情弹窗
  showOrderDetailModal: function(data) {
    // 格式化订单状态
    const statusMap = {
      0: '订单生成',
      1: '运单生成成功',
      20: '骑手接单',
      80: '骑手到店',
      2: '配送中',
      3: '已完成',
      4: '已取消',
      5: '配送异常'
    };

    const statusText = statusMap[data.order_status] || `状态${data.order_status}`;

    // 格式化金额（分转元）
    const totalAmount = data.order_total_amount_cent ? (data.order_total_amount_cent / 100).toFixed(2) : '0.00';
    const actualAmount = data.order_actual_amount_cent ? (data.order_actual_amount_cent / 100).toFixed(2) : '0.00';

    // 格式化预计到达时间
    let estimateTime = '未知';
    if (data.estimate_arrive_time) {
      const date = new Date(data.estimate_arrive_time);
      estimateTime = date.toLocaleString('zh-CN');
    }

    let content = `订单状态：${statusText}\n`;
    content += `订单总额：¥${totalAmount}\n`;
    content += `实际金额：¥${actualAmount}\n`;
    content += `配送距离：${data.order_distance ? (data.order_distance / 1000).toFixed(2) + 'km' : '未知'}\n`;
    content += `预计到达：${estimateTime}\n`;

    if (data.carrier_driver_name) {
      content += `骑手姓名：${data.carrier_driver_name}\n`;
    }
    if (data.carrier_driver_phone) {
      content += `骑手电话：${data.carrier_driver_phone}\n`;
    }
    if (data.fetch_code) {
      content += `取餐码：${data.fetch_code}\n`;
    }
    if (data.write_off_code) {
      content += `核销码：${data.write_off_code}\n`;
    }
    if (data.temperature) {
      content += `${data.temperature}\n`;
    }

    wx.showModal({
      title: '订单详情',
      content: content,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 显示骑手信息弹窗
  showRiderInfoModal: function(data) {
    let content = '';

    if (data.carrier_driver_name) {
      content += `骑手姓名：${data.carrier_driver_name}\n`;
    }
    if (data.carrier_driver_phone) {
      content += `联系电话：${data.carrier_driver_phone}\n`;
    }
    if (data.carrier_driver_id) {
      content += `骑手ID：${data.carrier_driver_id}\n`;
    }
    if (data.carrier_driver_longitude && data.carrier_driver_latitude) {
      content += `当前位置：${data.carrier_driver_latitude}, ${data.carrier_driver_longitude}\n`;
      content += `（高德坐标系）`;
    }

    if (!content) {
      content = '暂无骑手信息';
    }

    wx.showModal({
      title: '骑手信息',
      content: content,
      showCancel: data.carrier_driver_longitude && data.carrier_driver_latitude,
      cancelText: '查看地图',
      confirmText: '确定',
      success: (res) => {
        if (res.cancel && data.carrier_driver_longitude && data.carrier_driver_latitude) {
          // 打开地图查看骑手位置
          wx.openLocation({
            latitude: parseFloat(data.carrier_driver_latitude),
            longitude: parseFloat(data.carrier_driver_longitude),
            name: data.carrier_driver_name || '骑手位置',
            address: '骑手当前位置',
            scale: 18
          });
        }
      }
    });
  }
});
