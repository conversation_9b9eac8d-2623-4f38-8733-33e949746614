<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading || isLoadingConfig}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{isLoadingConfig ? '正在获取配置...' : '正在跳转...'}}</text>
  </view>
  
  <!-- 餐类型选择弹窗 -->
  <view class="dialog-mask" wx:if="{{showMealSelector}}" bindtap="hideMealSelector">
    <view class="dialog-container" catchtap="stopPropagation">
      <view class="dialog-header">
        <view class="dialog-title">选择餐类型</view>
        <view class="close-btn" bindtap="hideMealSelector">×</view>
      </view>
      
      <view class="meal-type-container">
        <button class="meal-type-btn {{selectedType === 'personal' ? 'active' : ''}}" bindtap="selectMealType" data-type="personal">
          <view class="btn-content">
            <text class="btn-icon">🚴🏻</text>
            <text class="btn-text">个人餐</text>
          </view>
        </button>
        
        <button class="meal-type-btn {{selectedType === 'employee' ? 'active' : ''}}" bindtap="selectMealType" data-type="employee">
          <view class="btn-content">
            <text class="btn-icon">🧑🏻‍💻</text>
            <text class="btn-text">企业餐</text>
          </view>
        </button>
      </view>
    </view>
  </view>

  <!-- 配置结果显示页面 -->
  <view class="config-result-container" wx:if="{{showConfigResult && configData}}">

    <!-- 推荐时段卡片居中容器 -->
    <view class="recommended-wrapper" wx:if="{{recommendedTimeSlot}}">
      <view class="recommended-card">
        <view class="recommended-header">
          <text class="recommended-icon">⭐</text>
          <text class="recommended-title">{{selectedType === 'personal' ? '个人自助餐' : '企业自助餐'}}</text>
        </view>
        
        <view class="recommended-content">
          <view class="recommended-date" wx:if="{{todayDate}}">{{todayDate}}</view>
          <view class="recommended-time">{{recommendedTimeSlot.time}}</view>
          <view class="recommended-info">
            <text class="product-name">{{recommendedTimeSlot.products[0].product_name}}</text>
            <text class="price">¥{{recommendedTimeSlot.price}}</text>
          </view>
          <view class="remaining-info">{{recommendedTimeSlot.remaining}}</view>
        </view>
        
        <!-- 个人餐人数选择 -->
        <view class="people-selector" wx:if="{{showPeopleInput}}">
          <view class="people-label">就餐人数</view>
          <view class="people-counter">
            <button class="counter-btn" bindtap="decreasePeople">-</button>
            <input
              class="people-input"
              type="number"
              value="{{diningPeople}}"
              bindinput="onPeopleChange"
              maxlength="2"
            />
            <button class="counter-btn" bindtap="increasePeople">+</button>
          </view>
        </view>

        <!-- 优惠券选择 -->
        <view class="coupon-section" wx:if="{{recommendedTimeSlot}}">
          <view class="coupon-row" bindtap="showCouponList">
            <text class="coupon-label">优惠券</text>
            <view class="coupon-content">
              <text class="coupon-text" wx:if="{{selectedCoupons.length === 0}}">请选择优惠券</text>
              <text class="coupon-text selected" wx:else>已选择{{selectedCoupons.length}}张优惠券</text>
              <text class="coupon-arrow">></text>
            </view>
          </view>

          <!-- 清空所有优惠券按钮 -->
          <view class="clear-all-coupons" wx:if="{{selectedCoupons.length > 0}}" bindtap="clearAllCoupons">
            <text class="clear-all-text">清空所有优惠券</text>
          </view>

          <!-- 显示已选择的优惠券详情 -->
          <view class="selected-coupons" wx:if="{{selectedCoupons.length > 0}}">
            <view class="coupon-item-detail" wx:for="{{selectedCoupons}}" wx:key="uniqueId">
              <view class="coupon-info">
                <text class="coupon-name">{{item.coupon.name}}</text>
                <text class="coupon-type">{{item.displayText}}</text>
              </view>
              <view class="coupon-actions">
                <text class="coupon-discount-amount">-¥{{item.discountAmount || 0}}</text>
                <view class="cancel-coupon-btn" bindtap="cancelCoupon" data-coupon-id="{{item.coupon_usage_record_id}}">
                  <text class="cancel-icon">×</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 金额明细 -->
        <view class="amount-section" wx:if="{{recommendedTimeSlot}}">
          <view class="subtotal-row">
            <text class="subtotal-label">小计</text>
            <text class="subtotal-amount">¥{{totalAmount}}</text>
          </view>
          <view class="discount-row" wx:if="{{couponDiscount > 0}}">
            <text class="discount-label">优惠券折扣</text>
            <text class="discount-amount">-¥{{couponDiscount}}</text>
          </view>
          <view class="total-row">
            <text class="total-label">总计</text>
            <text class="total-amount">¥{{finalAmount}}</text>
          </view>
        </view>
        
        <button 
          class="select-recommended-btn {{isProcessingPayment ? 'processing' : ''}}" 
          bindtap="selectRecommendedTimeSlot"
          disabled="{{isProcessingPayment}}"
        >
          <view class="payment-btn-content">
            <text>{{isProcessingPayment ? '处理中...' : '确认并支付'}}</text>
          </view>
        </button>
      </view>
    </view>

    <!-- 无推荐时段卡片居中容器 -->
    <view class="recommended-wrapper" wx:if="{{!recommendedTimeSlot}}">
      <view class="no-meal-card">
        <view class="recommended-header">
          <text class="recommended-icon">⭐</text>
          <text class="recommended-title">{{selectedType === 'personal' ? '个人自助餐' : '企业自助餐'}}</text>
        </view>
        
        <view class="no-meal-content">
          <view class="no-meal-date" wx:if="{{todayDate}}">{{todayDate}}</view>
          <view class="no-meal-message" wx:if="{{hasExistingReservation}}">已有预订记录！</view>
          <view class="no-meal-message" wx:elif="{{isNotOpenYet}}">当前未开餐！</view>
          <view class="no-meal-message" wx:else>当前未开餐！</view>
          <view class="remaining-info" wx:if="{{hasExistingReservation}}"></view>
          <view class="remaining-info" wx:elif="{{isNotOpenYet}}">请稍后再试或联系管理员</view>
          <view class="remaining-info" wx:else>请稍后再试或联系管理员</view>
        </view>
        
        <button class="confirm-no-meal-btn" bindtap="confirmNoMeal">
          <view class="confirm-btn-content">
            <text>返回</text>
          </view>
        </button>
      </view>
    </view>
  </view>

  <!-- 在支付选项弹窗前添加企业选择弹窗 -->

  <!-- 企业选择弹窗（仅企业餐使用） -->
  <view class="payment-modal" wx:if="{{showEnterpriseSelector}}">
    <view class="payment-container">
      <view class="payment-header">
        <text>选择企业账户</text>
        <view class="close-icon" bindtap="closeEnterpriseSelector">×</view>
      </view>
      <view class="payment-amount">¥{{payableAmount}}</view>
      
      <!-- 支付说明 -->
      <view class="payment-notice">
        <text class="notice-text">企业将支付 {{payableAmount - 3}} 元</text>
        <text class="notice-text">您需要额外支付 3 元</text>
      </view>
      
      <!-- 企业选择列表 -->
      <view class="enterprise-selection">
        <text class="enterprise-label">选择企业账户:</text>
        <block wx:if="{{enterpriseList.length > 0}}">
          <view class="enterprise-item {{selectedEnterprise == enterprise.id ? 'selected' : ''}}"
                wx:for="{{enterpriseList}}"
                wx:key="id"
                wx:for-item="enterprise"
                bindtap="selectEnterprise"
                data-id="{{enterprise.id}}">
            <view class="enterprise-info">
              <text class="enterprise-name">{{enterprise.company_name}}</text>
            </view>
            <view class="radio-btn"></view>
          </view>
        </block>
        <view class="no-enterprise" wx:else>
          <text>暂无可用企业</text>
        </view>
      </view>
      
      <button class="confirm-payment-btn {{isProcessingPayment ? 'processing' : ''}}" 
              bindtap="confirmEnterprisePayment"
              disabled="{{isProcessingPayment || !selectedEnterprise}}">
        {{isProcessingPayment ? '企业支付中...' : '确认企业支付'}}
      </button>
    </view>
  </view>

  <!-- 个人餐支付弹窗（也用于企业餐小金额） -->
  <view class="payment-modal" wx:if="{{showPaymentOptions}}">
    <view class="payment-container">
      <view class="payment-header">
        <text>{{selectedType === 'employee' && payableAmount <= 0.03 ? '企业餐支付' : '实付金额'}}</text>
        <view class="close-icon" bindtap="closePayment">×</view>
      </view>
      <view class="payment-amount">¥{{payableAmount}}</view>
      
      <!-- 企业餐小金额提示 -->
      <!--
      <view class="payment-notice" wx:if="{{selectedType === 'employee' && payableAmount <= 0.03}}">
        <text class="notice-text">订单金额不足0.03元，无需企业支付</text>
        <text class="notice-text">请您自行支付全部金额</text>
      </view>
      -->

      <view class="payment-methods">
        <text>选择支付方式:</text>
        
        <!-- 微信支付选项 -->
        <view class="payment-method {{selectedPaymentMethod === 'wxpay' ? 'selected' : ''}}"
              wx:if="{{payableAmount > 0}}"
              bindtap="selectPaymentMethod"
              data-method="wxpay">
          <view class="payment-method-left">
            <image src="https://vegan.yiheship.com/static/images/wechat_pay.png" class="method-icon"></image>
            <text class="payment-method-text">微信支付</text>
          </view>
          <view class="radio-container">
            <view class="radio-btn"></view>
          </view>
        </view>

        <!-- 个人账户选项 -->
        <view class="payment-method {{selectedPaymentMethod === 'balance' ? 'selected' : ''}} {{!canUseBalance ? 'disabled' : ''}}" 
              bindtap="{{canUseBalance ? 'selectPaymentMethod' : ''}}" 
              data-method="balance">
          <view class="payment-method-left">
            <image src="https://vegan.yiheship.com/static/images/balance-icon.png" class="method-icon"></image>
            <text class="payment-method-text">个人账户</text>
            <view class="balance-info">
              <text class="balance-text">余额</text>
              <text class="balance-amount">{{userBalance || 0}}元</text>
            </view>
          </view>
          <view class="radio-container">
            <view class="radio-btn"></view>
          </view>
          <text class="insufficient-tip" wx:if="{{!canUseBalance}}">余额不足</text>
        </view>
      </view>
      
      <button class="confirm-payment-btn {{isProcessingPayment ? 'processing' : ''}}" 
              bindtap="confirmPayment"
              disabled="{{isProcessingPayment}}">
        {{isProcessingPayment ? '处理中...' : '确认支付'}}
      </button>
    </view>
  </view>

  <!-- 企业餐混合支付弹窗（仅用于大金额企业餐） -->
  <view class="payment-modal" wx:if="{{showCombinedPayment}}">
    <view class="combined-payment-container">
      <view class="payment-header">
        <text>企业混合支付</text>
        <view class="close-icon" bindtap="closeCombinedPayment">×</view>
      </view>
      <view class="payment-amount">¥{{payableAmount}}</view>
      
      <!-- 支付说明 -->
      <view class="payment-notice">
        <text class="notice-text">企业将支付 {{payableAmount - personalPaymentAmount}} 元</text>
        <text class="notice-text">您需要额外支付 {{personalPaymentAmount}} 元</text>
      </view>
      
      <!-- 企业选择部分 -->
      <view class="payment-section">
        <view class="section-title">选择企业账户</view>
        <view class="enterprise-selection">
          <!-- 在企业选择部分添加晚餐提示 -->
          <view class="enterprise-selection">
            <!-- 晚餐限制提示 -->
            <view class="dinner-notice" wx:if="{{recommendedTimeSlot.rule_meal_type === 'dinner'}}">
              <text class="notice-text">⚠️ 晚餐时段仅限以下企业支付</text>
            </view>

            <block wx:if="{{enterpriseList.length > 0}}">
              <view class="enterprise-item {{selectedEnterprise == enterprise.id ? 'selected' : ''}}"
                    wx:for="{{enterpriseList}}"
                    wx:key="id"
                    wx:for-item="enterprise"
                    bindtap="selectEnterprise"
                    data-id="{{enterprise.id}}">
                <view class="enterprise-info">
                  <text class="enterprise-name">{{enterprise.company_name}}</text>
                </view>
                <view class="radio-btn"></view>
              </view>
            </block>

            <view class="no-enterprise" wx:else>
              <text>{{recommendedTimeSlot.rule_meal_type === 'dinner' ? '晚餐暂无可用企业' : '暂无可用企业'}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 个人支付方式选择部分 -->
      <view class="payment-section">
        <view class="section-title">选择个人支付方式 (¥{{personalPaymentAmount}})</view>
        <view class="payment-methods">
          <!-- 个人账户选项 -->
          <view class="payment-method {{selectedPersonalPaymentMethod === 'balance' ? 'selected' : ''}} {{!canUseBalanceForPersonal ? 'disabled' : ''}}" 
                bindtap="{{canUseBalanceForPersonal ? 'selectPersonalPaymentMethod' : ''}}" 
                data-method="balance">
            <view class="payment-method-left">
              <image src="https://vegan.yiheship.com/static/images/balance-icon.png" class="method-icon"></image>
              <text class="payment-method-text">个人账户</text>
              <view class="balance-info">
                <text class="balance-text">余额</text>
                <text class="balance-amount">{{userBalance || 0}}元</text>
              </view>
            </view>
            <view class="radio-container">
              <view class="radio-btn"></view>
            </view>
            <text class="insufficient-tip" wx:if="{{!canUseBalanceForPersonal}}">余额不足</text>
          </view>

          <!-- 微信支付选项 -->
          <view class="payment-method {{selectedPersonalPaymentMethod === 'wxpay' ? 'selected' : ''}}"
                wx:if="{{personalPaymentAmount > 0}}"
                bindtap="selectPersonalPaymentMethod"
                data-method="wxpay">
            <view class="payment-method-left">
              <image src="https://vegan.yiheship.com/static/images/wechat_pay.png" class="method-icon"></image>
              <text class="payment-method-text">微信支付</text>
            </view>
            <view class="radio-container">
              <view class="radio-btn"></view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 一次性确认支付按钮 -->
      <button class="confirm-payment-btn {{isProcessingPayment ? 'processing' : ''}}" 
              bindtap="confirmCombinedPayment"
              disabled="{{isProcessingPayment || !selectedEnterprise}}">
        {{isProcessingPayment ? '支付中...' : '确认支付'}}
      </button>
    </view>
  </view>

  <!-- 优惠券列表组件 -->
  <coupon-list
    visible="{{showCouponList}}"
    order-amount="{{totalAmount}}"
    selected-coupon-ids="{{selectedCouponIds}}"
    allow-multiple="{{true}}"
    products="{{products}}"
    source="admin_onsite"
    bind:close="hideCouponList"
    bind:select="onCouponSelect"
  ></coupon-list>
</view>