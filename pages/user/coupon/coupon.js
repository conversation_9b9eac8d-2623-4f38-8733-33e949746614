Page({
  data: {
    loading: true,
    currentTab: 'available',
    tabs: [
      { key: 'available', name: '可使用' },
      { key: 'unavailable', name: '不可用' },
      { key: 'used', name: '已使用' }
    ],
    availableCoupons: [],
    unavailableCoupons: [],
    usedCoupons: [],
    orderAmount: 0,
    summary: null,
    userInfo: null
  },

  onLoad() {
    this.loadCoupons();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadCoupons();
  },

  /**
   * 加载优惠券数据
   */
  loadCoupons() {
    this.setData({ loading: true });

    const app = getApp();
    const token = wx.getStorageSync('token');

    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      this.setData({ loading: false });
      return;
    }

    // 使用新的 user-coupons API获取优惠券列表
    wx.request({
      url: `${app.globalData.baseUrl}/coupon/user-coupons`,
      method: 'GET',
      header: {
        'Content-Type': 'application/json',
        'token': token
      },
      success: (res) => {
        console.log('优惠券数据:', res.data);
        if (res.data && res.data.status === 200) {
          this.processCouponData(res.data.data);
        } else {
          wx.showToast({
            title: res.data.message || '获取优惠券失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('获取优惠券失败:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ loading: false });
      }
    });
  },

  /**
   * 处理优惠券数据
   */
  processCouponData(data) {
    const { available_coupons = [], unavailable_coupons = [], used_coupons = [], summary = {}, user_info = {} } = data;

    // 处理可用优惠券
    const availableCoupons = this.processCouponsData(available_coupons);

    // 处理不可用优惠券
    const unavailableCoupons = this.processCouponsData(unavailable_coupons);

    // 处理已使用优惠券
    const usedCoupons = this.processCouponsData(used_coupons);

    this.setData({
      availableCoupons,
      unavailableCoupons,
      usedCoupons,
      summary,
      userInfo: user_info
    });
  },

  /**
   * 处理优惠券数据，添加格式化的折扣文本和唯一ID
   */
  processCouponsData(coupons) {
    console.log('处理优惠券数据，输入:', coupons);

    if (!Array.isArray(coupons)) {
      console.error('优惠券数据不是数组:', coupons);
      return [];
    }

    return coupons.map(item => {
      console.log('处理单个优惠券:', item);

      const couponUsageRecord = item.coupon_usage_record || {};
      const coupon = item.coupon || {};
      const couponBatch = item.coupon_batch || {};
      const unavailableReasons = item.unavailable_reasons || [];

      let displayText = '';
      let conditionText = '';
      let typeText = '';

      // 根据优惠券类型设置显示文本
      if (coupon.type === 'cash') {
        // 现金券
        displayText = `¥${coupon.amount || 0}`;
        conditionText = coupon.name || '现金券';
        typeText = '现金券';
      } else if (coupon.type === 'full_reduction') {
        // 满减券
        displayText = `¥${coupon.reduction_amount || 0}`;
        conditionText = coupon.name || '满减优惠';
        typeText = '满减券';
      } else if (coupon.type === 'discount') {
        // 折扣券
        const discountRate = Math.round((coupon.discount_rate || 1) * 10);
        displayText = `${discountRate}折`;
        conditionText = coupon.name || '折扣优惠';
        typeText = '折扣券';
      } else {
        // 其他类型
        displayText = coupon.name || '优惠券';
        conditionText = coupon.description || '';
        typeText = '优惠券';
      }

      // 格式化有效期时间
      let formattedStartTime = '';
      let formattedEndTime = '';
      let formattedUsedTime = '';

      if (couponBatch.start_time) {
        try {
          const startTime = new Date(couponBatch.start_time);
          formattedStartTime = `${startTime.getFullYear()}-${String(startTime.getMonth() + 1).padStart(2, '0')}-${String(startTime.getDate()).padStart(2, '0')}`;
        } catch (e) {
          console.error('解析开始时间失败:', e);
          formattedStartTime = '无效时间';
        }
      }

      if (couponBatch.end_time) {
        try {
          const endTime = new Date(couponBatch.end_time);
          formattedEndTime = `${endTime.getFullYear()}-${String(endTime.getMonth() + 1).padStart(2, '0')}-${String(endTime.getDate()).padStart(2, '0')}`;
        } catch (e) {
          console.error('解析有效期时间失败:', e);
          formattedEndTime = '无效时间';
        }
      }

      if (couponUsageRecord.used_at) {
        try {
          const usedTime = new Date(couponUsageRecord.used_at);
          formattedUsedTime = `${usedTime.getFullYear()}-${String(usedTime.getMonth() + 1).padStart(2, '0')}-${String(usedTime.getDate()).padStart(2, '0')}`;
        } catch (e) {
          console.error('解析使用时间失败:', e);
          formattedUsedTime = '无效时间';
        }
      }

      // 生成唯一ID
      const uniqueId = couponUsageRecord.id ||
                      coupon.id ||
                      `${coupon.type}_${Date.now()}_${Math.random()}`;

      return {
        ...item,
        displayText,
        conditionText,
        typeText,
        formattedStartTime,
        formattedEndTime,
        formattedUsedTime,
        uniqueId,
        coupon_usage_record: couponUsageRecord,
        coupon: coupon,
        coupon_batch: couponBatch,
        unavailable_reasons: unavailableReasons
      };
    });
  },

  /**
   * 切换标签页
   */
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
  },

  /**
   * 使用优惠券 - 跳转到我要预订页面
   */
  useCoupon(e) {
    const coupon = e.currentTarget.dataset.coupon;
    
    wx.showModal({
      title: '使用优惠券',
      content: '是否前往预订页面使用此优惠券？',
      success: (res) => {
        if (res.confirm) {
          // 跳转到我要预订页面
          wx.navigateTo({
            url: '/pages/booking/booking'
          });
        }
      }
    });
  },

  /**
   * 返回按钮
   */
  navigateBack: function() {
    wx.navigateBack({
      delta: 1
    });
  }
});
