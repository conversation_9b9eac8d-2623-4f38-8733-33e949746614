/* 页面容器 */
.coupon-center-container {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 标签页 */
.coupon-tabs {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #4080ff;
  font-weight: 600;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #4080ff;
  border-radius: 2rpx;
}

/* 内容区域 */
.coupon-content {
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #999;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #4080ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 优惠券列表 */
.coupon-list {
  padding-bottom: 20rpx;
}

/* 优惠券项目 */
.coupon-item {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.coupon-item.available {
  border: 2rpx solid transparent;
}

.coupon-item.disabled,
.coupon-item.used {
  opacity: 0.6;
}

/* 优惠券左侧金额区域 */
.coupon-left {
  width: 160rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
  position: relative;
}

/* 不同类型优惠券的渐变色 */
.coupon-item[data-type="discount"] .coupon-left {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
}

.coupon-item[data-type="cash"] .coupon-left {
  //background: linear-gradient(135deg, #4ecdc4, #44a08d);
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
}

.coupon-item[data-type="full_reduction"] .coupon-left {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  //background: linear-gradient(135deg, #45b7d1, #96c93d);
}

.coupon-item.disabled .coupon-left {
  background: #ccc;
}

.coupon-item.used .coupon-left {
  background: #999;
}

.coupon-left::after {
  content: "";
  position: absolute;
  right: -10rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 20rpx;
  height: 20rpx;
  background: #fff;
  border-radius: 50%;
}

.coupon-amount {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1;
}

.coupon-condition {
  font-size: 20rpx;
  margin-top: 8rpx;
  opacity: 0.9;
}

/* 优惠券右侧信息区域 */
.coupon-right {
  flex: 1;
  padding: 24rpx 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.coupon-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.coupon-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.coupon-time {
  font-size: 22rpx;
  color: #999;
}

.coupon-unavailable {
  font-size: 22rpx;
  color: #ff6b6b;
  margin-top: 8rpx;
}

/* 按钮区域 */
.coupon-action {
  width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
}

.use-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #4080ff;
  background: transparent;
  border: none;
  padding: 0;
  font-weight: 600;
}

.use-btn:active {
  opacity: 0.7;
}

.disabled-btn,
.used-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #999;
  background: transparent;
  border: none;
  padding: 0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部按钮容器 */
.bottom-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 40rpx;
  border-top: 1rpx solid #e0e0e0;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.back-button {
  width: 100%;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.back-button:active {
  background: #0056cc;
}

/* 为底部按钮留出空间 */
.coupon-center-container {
  padding-bottom: 120rpx;
}
