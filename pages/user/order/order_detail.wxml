<import src="../../../templates/copyright.wxml" />

<view class="readlog-container">
  <view class='user-cell'>
    <view class="account-info">
      <block wx:if="{{isLogin}}">
        <!-- 订单详情 -->
        <view class="order-detail" wx:if="{{order && !loading}}">
          <!-- 到店指引 -->
          <view class="store-guide-section" wx:if="{{order.type === 'takeout'}}">
            <view class="section-title">
              <text class="title-icon">📍</text>
              <text>到店指引</text>
            </view>
            <view class="store-guide-content">
              <view class="store-info-row">
                <view class="store-basic-info">
                  <view class="store-name">{{storeInfo.name}}</view>
                  <view class="store-address">{{storeInfo.address}}</view>
                </view>
                <view class="store-distance" wx:if="{{currentDistance}}">
                  <text class="distance-text">{{currentDistance}}</text>
                  <text class="distance-unit">km</text>
                </view>
              </view>

              <view class="pickup-time-row" wx:if="{{pickupTimeInfo}}">
                <view class="pickup-time-label">自提时间：</view>
                <view class="pickup-time-value">{{pickupTimeInfo}}</view>
              </view>

              <view class="guide-actions">
                <button class="guide-btn location-btn" bindtap="getCurrentLocation" disabled="{{locationLoading}}">
                  <text class="btn-icon">📍</text>
                  <text>{{locationLoading ? '定位中...' : '获取距离'}}</text>
                </button>
                <button class="guide-btn navigate-btn" bindtap="navigateToStore">
                  <text class="btn-icon">🧭</text>
                  <text>到店导航</text>
                </button>
              </view>
            </view>
          </view>

          <!-- 订单基本信息 -->
          <view class="detail-section">
            <view class="section-title">订单信息</view>
            <view class="detail-row">
              <text class="detail-label">订单号：</text>
              <text class="detail-value">{{order.order_no}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">订单类型：</text>
              <text class="detail-value">{{order.typeText}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">订单状态：</text>
              <text class="detail-value status {{order.status}}">{{order.statusText}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">支付状态：</text>
              <text class="detail-value payment-status">{{order.paymentStatusText}}</text>
            </view>
          </view>

          <!-- 金额信息 -->
          <view class="detail-section">
            <view class="section-title">金额信息</view>
            <view class="detail-row">
              <text class="detail-label">订单金额：</text>
              <text class="detail-value amount">¥{{order.total_amount}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">应付金额：</text>
              <text class="detail-value amount payable">¥{{order.payable_amount}}</text>
            </view>
            <view class="detail-row" wx:if="{{order.actual_amount_paid > 0}}">
              <text class="detail-label">实付金额：</text>
              <text class="detail-value amount paid">¥{{order.actual_amount_paid}}</text>
            </view>
            <view class="detail-row" wx:if="{{order.total_discount_amount > 0}}">
              <text class="detail-label">优惠金额：</text>
              <text class="detail-value amount discount">-¥{{order.total_discount_amount}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">支付方式：</text>
              <text class="detail-value">{{order.paymentMethodText}}</text>
            </view>
            <view class="detail-row" wx:if="{{order.pricing_remark}}">
              <text class="detail-label">价格备注：</text>
              <text class="detail-value">{{order.pricing_remark}}</text>
            </view>
          </view>

          <!-- 订单商品 -->
          <view class="detail-section" wx:if="{{order.items && order.items.length > 0}}">
            <view class="section-title">订单商品</view>
            <view class="item-list">
              <view class="item-row" wx:for="{{order.items}}" wx:key="id">
                <view class="item-info">
                  <view class="item-name">{{item.product_name}}</view>
                  <view class="item-meta">
                    <text class="item-price">单价：¥{{item.final_price}}</text>
                    <text class="item-quantity">数量：{{item.quantity}}</text>
                  </view>
                  <!--
                  <view class="item-meta" wx:if="{{item.final_price !== item.price}}">
                    <text class="item-final-price">优惠价：¥{{item.final_price}}</text>
                  </view>
                  -->
                  <view class="item-meta" wx:if="{{item.pricing_remark}}">
                    <text class="item-remark">{{item.pricing_remark}}</text>
                  </view>
                </view>
                <view class="item-amount">
                  <text class="subtotal">¥{{item.final_price * item.quantity}}</text>
                  <text class="payable-amount" wx:if="{{item.payable_amount !== item.final_price * item.quantity}}">
                    实付：¥{{item.payable_amount}}
                  </text>
                </view>
              </view>
            </view>
          </view>

          <!-- 优惠券信息 -->
          <view class="detail-section" wx:if="{{order.coupon_discounts && order.coupon_discounts.length > 0}}">
            <view class="section-title">优惠券</view>
            <view class="coupon-list">
              <view class="coupon-row" wx:for="{{order.coupon_discounts}}" wx:key="coupon_usage_record_id">
                <view class="coupon-info">
                  <view class="coupon-name">{{item.coupon_name}}</view>
                  <view class="coupon-time">使用时间：{{item.used_at}}</view>
                </view>
                <view class="coupon-discount">-¥{{item.discount_amount}}</view>
              </view>
            </view>
          </view>

          <!-- 时间信息 -->
          <view class="detail-section">
            <view class="section-title">时间信息</view>
            <view class="detail-row">
              <text class="detail-label">创建时间：</text>
              <text class="detail-value">{{order.createdAtText}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">更新时间：</text>
              <text class="detail-value">{{order.updatedAtText}}</text>
            </view>
            <view class="detail-row" wx:if="{{order.payment_time}}">
              <text class="detail-label">支付时间：</text>
              <text class="detail-value">{{order.paymentTimeText}}</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="action-section" wx:if="{{order.canCancel}}">
            <button class="cancel-btn" bindtap="cancelOrder">取消订单</button>
          </view>
        </view>

        <!-- 加载状态 -->
        <view class="loading-state" wx:if="{{loading}}">
          <view class="loading-icon">⏳</view>
          <view class="loading-text">加载中...</view>
        </view>

      </block>
      
      <!-- 未登录状态 -->
      <block wx:else>
        <view class="user-profile">
          <image class="avatar-circle" src="/images/gravatar.png" background-size="cover"></image>
          <button class="login-btn" bindtap="handleLogin">点击登录</button>
        </view>
      </block>
    </view>
  </view>

  <!-- 返回按钮 -->
  <view class="bottom-button-container">
    <button class="back-button" bindtap="navigateBack">返回</button>
  </view>
</view>
