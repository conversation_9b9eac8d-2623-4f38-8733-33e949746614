<import src="../../../templates/copyright.wxml" />

<view class="readlog-container">
  <view class='user-cell'>
    <view class="account-info">
      <block wx:if="{{isLogin}}">
        <!-- 订单列表 -->
        <view class="order-list" wx:if="{{orders.length > 0}}">
          <view class="order-item" wx:for="{{orders}}" wx:key="id">
            <view class="order-header" bindtap="viewOrderDetail" data-id="{{item.id}}">
              <view class="order-info">
                <view class="order-no">订单号：{{item.order_no}}</view>
                <view class="order-type">{{item.typeText}}</view>
              </view>
              <view class="order-status">
                <view class="status-text {{item.status}}">{{item.statusText}}</view>
                <view class="payment-status">{{item.paymentStatusText}}</view>
              </view>
            </view>
            
            <view class="order-content" bindtap="viewOrderDetail" data-id="{{item.id}}">
              <view class="order-amount">
                <view class="amount-row">
                  <text class="amount-label">订单金额：</text>
                  <text class="amount-value">¥{{item.total_amount}}</text>
                </view>
                <view class="amount-row" wx:if="{{item.payable_amount !== item.total_amount}}">
                  <text class="amount-label">应付金额：</text>
                  <text class="amount-value payable">¥{{item.payable_amount}}</text>
                </view>
                <view class="amount-row" wx:if="{{item.actual_amount_paid > 0}}">
                  <text class="amount-label">实付金额：</text>
                  <text class="amount-value paid">¥{{item.actual_amount_paid}}</text>
                </view>
              </view>
              
              <view class="order-meta">
                <view class="meta-row">
                  <text class="meta-label">支付方式：</text>
                  <text class="meta-value">{{item.paymentMethodText}}</text>
                </view>
                <view class="meta-row">
                  <text class="meta-label">创建时间：</text>
                  <text class="meta-value">{{item.createdAtText}}</text>
                </view>
                <view class="meta-row" wx:if="{{item.payment_time}}">
                  <text class="meta-label">支付时间：</text>
                  <text class="meta-value">{{item.paymentTimeText}}</text>
                </view>
              </view>
            </view>

            <!-- 操作栏 -->
            <view class="order-actions">
              <button class="action-btn detail-btn" bindtap="viewOrderDetail" data-id="{{item.id}}">
                详情
              </button>
              <button class="action-btn cancel-btn" wx:if="{{item.status === 'pending'}}" bindtap="cancelOrder" data-id="{{item.id}}" data-order-no="{{item.order_no}}">
                取消
              </button>
              <button class="action-btn refund-btn" wx:if="{{item.canRefund}}" bindtap="refundOrder" data-id="{{item.id}}" data-order-no="{{item.order_no}}">
                退订
              </button>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{orders.length === 0 && !loading}}">
          <view class="empty-icon">📋</view>
          <view class="empty-text">暂无订单记录</view>
        </view>

        <!-- 加载状态 -->
        <view class="loading-state" wx:if="{{loading && orders.length === 0}}">
          <view class="loading-icon">⏳</view>
          <view class="loading-text">加载中...</view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{loading && orders.length > 0}}">
          <view class="loading-icon">⏳</view>
          <text>加载更多中...</text>
        </view>

        <!-- 没有更多 -->
        <view class="no-more" wx:if="{{!hasMore && orders.length > 0 && !loading}}">
          <text>没有更多订单了</text>
        </view>

      </block>
      
      <!-- 未登录状态 -->
      <block wx:else>
        <view class="user-profile">
          <image class="avatar-circle" src="/images/gravatar.png" background-size="cover"></image>
          <button class="login-btn" bindtap="handleLogin">点击登录</button>
        </view>
      </block>
    </view>
  </view>

  <!-- 返回按钮 -->
  <view class="bottom-button-container">
    <button class="back-button" bindtap="navigateBack">返回</button>
  </view>
</view>
