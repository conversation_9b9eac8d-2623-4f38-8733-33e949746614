<!--pages/booking_business_pay/booking_business_pay.wxml-->
<view class="page-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text>加载中...</text>
  </view>
  <block wx:else>
    <!-- 商家信息 -->
    <view class="store-info">
      <view class="store-banner">
        <image class="store-banner-image" src="https://vegan.yiheship.com/static/images/002.png" mode="aspectFill"></image>
        <view class="store-banner-overlay"></view>
      </view>
      <view class="store-card">
        <view class="store-avatar-container">
          <image class="store-avatar" src="{{storeInfo.image || '/images/default-store.png'}}" mode="aspectFill"></image>
        </view>
        <view class="store-details">
          <view class="store-header">
            <view class="store-title">
              <text class="store-name">{{storeInfo.name || '素食餐厅'}}</text>
              <view class="store-tag">营业中</view>
            </view>
            <!--<view class="store-rating">
              <text class="rating-score">{{storeInfo.rating || '5.0'}}</text>
              <text class="rating-count">{{storeInfo.ratingCount || '0'}}条评价</text>
            </view>-->
          </view>
          <view class="store-address-container">
            <text class="store-address">{{storeInfo.address || '地址信息'}}</text>
          </view>
          <!-- 添加联系电话 -->
          <view class="store-phone-container">
              <text class="store-phone">联系电话: 18802053864</text>
              <view class="call-button" bindtap="makePhoneCall">拨打</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-info-section">
      <view class="section-title">订单信息</view>
      <view class="order-info-item">
        <text class="info-label">订单号:</text>
        <text class="info-value">{{orderNo}}</text>
      </view>
      <view class="order-info-item">
        <text class="info-label">预约日期:</text>
        <text class="info-value">{{bookingDate}}</text>
      </view>
      <view class="order-info-item">
        <text class="info-label">预约时间:</text>
        <text class="info-value">{{bookingTime}}</text>
      </view>
      <view class="order-info-item">
        <text class="info-label">联系人:</text>
        <text class="info-value">{{contactName}}</text>
      </view>
      <view class="order-info-item">
        <text class="info-label">联系电话:</text>
        <text class="info-value">{{contactPhone}}</text>
      </view>
      <view class="order-info-item">
        <text class="info-label">用餐人数:</text>
        <text class="info-value">{{peopleCount}}人</text>
      </view>
    </view>

    <!-- 订单商品列表 -->
    <view class="order-items-section">
      <view class="section-title">订单商品</view>
      <view class="order-item" wx:for="{{orderItems}}" wx:key="dish_id">
        <view class="order-item-left">
          <image class="item-image" src="{{item.image ? (item.image.indexOf('http') === 0 ? item.image : baseUrlHost + item.image) : '/images/default-dish.png'}}" mode="aspectFill"></image>
          <view class="item-info">
            <text class="item-name">{{item.name}}</text>
            <text class="item-price">¥{{item.price}}</text>
          </view>
        </view>
        <view class="item-right">
          <view class="item-quantity">x{{item.quantity}}</view>
          <view class="item-subtotal">¥{{item.price * item.quantity}}</view>
        </view>
      </view>
    </view>

    <!-- 订单金额 -->
    <view class="order-amount-section">
      <view class="amount-item">
        <text class="amount-label">商品总金额</text>
        <text class="amount-value">¥{{currentItemsAmount}}</text>
      </view>
      <view class="amount-item">
        <text class="amount-label">已支付金额</text>
        <text class="amount-value">¥{{totalAmount}}</text>
      </view>
      <view class="amount-item">
        <text class="amount-label">待支付金额</text>
        <text class="amount-value">¥{{unpaidAmount}}</text>
      </view>
      <view class="amount-item total">
        <text class="amount-label">应付金额</text>
        <text class="amount-value highlight">¥{{unpaidAmount}}</text>
      </view>
    </view>

    <!-- 添加空白区域 -->
    <view class="spacer"></view>
    
    <!-- 支付按钮 -->
    <view class="payment-button-container">
      <button class="payment-button" bindtap="showPaymentOptions">支付账单</button>
    </view>

    <!-- 底部添加空白区域 -->
    <view class="spacer-pay"></view>

    <!-- 支付选项弹窗 -->
    <view class="payment-modal" wx:if="{{showPaymentOptions}}">
      <view class="payment-container">
        <view class="payment-header">
          <text>实付金额</text>
          <view class="close-icon" bindtap="closePayment">×</view>
        </view>
        <view class="payment-amount">¥{{payableAmount}}</view>
        
        <view class="payment-methods">
          <view class="payment-methods-header">
            <text>选择支付方式:</text>
            <text class="payment-notice-red">(支付方式需与原订单一致)</text>
          </view>

          <!-- 微信支付选项 - 只在原订单是微信支付时显示 -->
          <view class="payment-method {{selectedPaymentMethod === 'wxpay' ? 'selected' : ''}}"
                wx:if="{{originalPaymentMethod === 'wechat_pay' && unpaidAmount > 0}}"
                bindtap="selectPaymentMethod"
                data-method="wxpay">
            <view class="payment-method-left">
              <image src="https://vegan.yiheship.com/static/images/wechat_pay.png" class="method-icon"></image>
              <text class="payment-method-text">微信支付</text>
            </view>
            <view class="radio-container">
              <view class="radio-btn"></view>
            </view>
          </view>

          <!-- 个人账户选项 - 只在原订单是个人账户支付时显示 -->
          <view class="payment-method {{selectedPaymentMethod === 'balance' ? 'selected' : ''}} {{!canUseBalance ? 'disabled' : ''}}" 
                wx:if="{{originalPaymentMethod === 'account_balance'}}"
                bindtap="{{canUseBalance ? 'selectPaymentMethod' : ''}}"
                data-method="balance">
            <view class="payment-method-left">
              <image src="https://vegan.yiheship.com/static/images/balance-icon.png" class="method-icon"></image>
              <text class="payment-method-text">个人账户</text>
              <view class="balance-info">
                <text class="balance-text">余额</text>
                <text class="balance-amount">{{userBalance || 0}}元</text>
              </view>
            </view>
            <view class="radio-container">
              <view class="radio-btn"></view>
            </view>
            <text class="insufficient-tip" wx:if="{{!canUseBalance}}">余额不足</text>
          </view>

          <!-- 企业支付选项 - 只在原订单是企业支付时显示 -->
          <view class="payment-method {{selectedPaymentMethod === 'biz_enterprise' ? 'selected' : ''}}" 
                wx:if="{{originalPaymentMethod === 'biz_enterprise' || originalPayEnterpriseId}}"
                bindtap="selectPaymentMethod"
                data-method="biz_enterprise">
            <view class="payment-method-left">
              <image src="https://vegan.yiheship.com/static/images/enterprise-icon.png" class="method-icon"></image>
              <text class="payment-method-text">企业支付</text>
            </view>
            <view class="radio-container">
              <view class="radio-btn"></view>
            </view>
          </view>

          <!-- 企业选择列表（当选择企业支付时显示，且只显示原支付企业） -->
          <view class="enterprise-list" wx:if="{{selectedPaymentMethod === 'biz_enterprise' && (originalPaymentMethod === 'biz_enterprise' || originalPayEnterpriseId)}}">
            <text class="enterprise-label">选择企业:</text>
            <block wx:if="{{enterpriseList.length > 0}}">
              <view class="enterprise-item {{selectedEnterprise === enterprise.id ? 'selected' : ''}}" 
                    wx:for="{{enterpriseList}}" 
                    wx:key="id" 
                    wx:for-item="enterprise"
                    wx:if="{{enterprise.id === originalPayEnterpriseId}}"
                    bindtap="selectEnterprise"
                    data-id="{{enterprise.id}}">
                <text class="enterprise-name">{{enterprise.company_name}}</text>
                <view class="radio-btn"></view>
              </view>
            </block>
            <view class="no-enterprise" wx:if="{{!originalPayEnterpriseId}}">
              <text>暂无可用企业</text>
            </view>
          </view>
        </view>
        
        <button class="confirm-payment-btn" bindtap="confirmPayment">确认支付</button>
      </view>
    </view>

  </block>
</view>