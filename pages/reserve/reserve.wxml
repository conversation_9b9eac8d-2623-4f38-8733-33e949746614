<!--pages/reserve/reserve.wxml-->
<view class="container {{isPopupShow ? 'popup-show' : ''}}">
  <!-- 顶部导航 -->
  <view class="nav">
    <view class="nav-item {{currentTab == 0 ? 'active' : ''}}" bindtap="switchTab" data-tab="0">今天</view>
    <view class="nav-item {{currentTab == 1 ? 'active' : ''}}" bindtap="switchTab" data-tab="1">明天</view>
    <view class="nav-item {{currentTab == 2 ? 'active' : ''}}" bindtap="switchTab" data-tab="2">全部</view>
    <view class="nav-item {{currentTab == 3 ? 'active' : ''}}" bindtap="switchTab" data-tab="3">筛选</view>
    <!--<view class="nav-item filter-btn" bindtap="showFilterPopup">筛选</view>-->
  </view>

  <!-- 日期显示 -->
  <view class="date">共 {{ count }} 条记录</view>

  <!-- 预约列表 -->
  <view class="reserve-list">
    <block wx:for="{{orders}}" wx:key="index">
      <view class="reserve-item">
        <view class="title">{{item.title}}</view>
        <!-- <view class="info-row">
          <text class="label">报餐状态：</text>
          <text class="value {{item.status === 'pending' || item.status === 'cancelled' || item.status === 'refunded' || item.status === 'refunded_partial' ? 'error' : 'success'}}">{{item.status === 'paid' ? '已支付' :item.status === 'shipped' ? '已发货' :item.status === 'delivered' ? '已送达' : item.status === 'verified' ? '已就餐' : item.status === 'completed' ? '已完成' : item.status === 'pending' ? '待支付' : item.status === 'cancelled' ? '已取消' : item.status === 'refunded' ? '已退款' : item.status === 'refunded_partial' ? '已退款' : item.status}}</text>
        </view> -->
        <view class="info-row">
          <text class="label">报餐人员：</text>
          <text class="value">{{item.person}}</text>
        </view>
        <view class="info-row">
          <text class="label">就餐时间：</text>
          <text class="value">{{item.date}}</text>
        </view>
        <view class="info-row">
          <text class="label">就餐人数：</text>
          <text class="value">{{item.people}} 人</text>
        </view>
        <view class="info-row">
          <text class="label">订单价格：</text>
          <text class="price-value">{{item.price}} 元</text>
        </view>

        <view class="info-row">
          <text class="label">预订状态：</text>
          <text class="value {{item.display_info.status_class}}">{{item.display_info.status_text}}</text>
        </view>
        <view class="actions">
          <!-- 订单按钮 - 仅商务餐显示 -->
          <view class="action-btn"
                wx:if="{{item.can_show_order_detail}}"
                bindtap="showOrderDetail"
                data-order="{{item}}">
            订单
          </view>

          <!-- 加菜/详情按钮 -->
          <view class="action-btn {{!item.can_edit ? 'disabled-btn' : ''}}"
                bindtap="{{item.can_edit ? (item.is_business_meal ? 'goToEdit' : 'showDetail') : ''}}"
                data-order="{{item}}"
                data-order-id="{{item.order_id}}">
            {{item.is_business_meal ? '加菜' : '详情'}}
          </view>

          <!-- 二维码按钮 -->
          <view class="action-btn {{!item.can_show_qr ? 'disabled-btn' : ''}}"
                bindtap="{{item.can_show_qr ? 'showQrCode' : ''}}"
                data-order="{{item}}">二维码</view>

          <!-- 取消按钮 -->
          <view class="action-btn {{!item.can_cancel ? 'disabled-btn' : ''}}"
                bindtap="{{item.can_cancel ? 'showCancelConfirm' : ''}}"
                data-order="{{item}}">
            {{item.is_business_meal ? '取消预约' : '取消报餐'}}
          </view>
        </view>
      </view>
    </block>
  </view>
</view>

<!-- 在container末尾添加弹窗组件 -->
<view class="popup" wx:if="{{showPopup}}" catchtouchmove="preventTouchMove">
  <view class="popup-mask" bindtap="hidePopup"></view>
  <view class="popup-content">
    <view class="popup-header">
      <text>{{popupTitle}}</text>
      <view class="close-btn" bindtap="hidePopup">×</view>
    </view>
    <view class="popup-body">
      <!-- <view class="info-row">
        <text class="label">报餐状态：</text>
        <text class="value {{selectedOrder.status === 'pending' || selectedOrder.status === 'cancelled' || selectedOrder.status === 'refunded' || selectedOrder.status === 'refunded_partial' ? 'error' : 'success'}}">{{selectedOrder.status === 'paid' ? '已支付' :selectedOrder.status === 'shipped' ? '已发货' :selectedOrder.status === 'delivered' ? '已送达' : selectedOrder.status === 'verified' ? '已就餐' : selectedOrder.status === 'completed' ? '已完成' : selectedOrder.status === 'pending' ? '待支付' : selectedOrder.status === 'cancelled' ? '已取消' : selectedOrder.status === 'refunded' ? '已退款' : selectedOrder.status === 'refunded_partial' ? '已退款' : selectedOrder.status}}</text>
      </view> -->
      <view class="info-row">
        <text class="label">报餐人员：</text>
        <text class="value">{{selectedOrder.person}}</text>
      </view>
      <view class="info-row">
        <text class="label">就餐时间：</text>
        <text class="value">{{selectedOrder.date}}</text>
      </view>
      <view class="info-row">
        <text class="label">就餐人数：</text>
        <text class="value">{{selectedOrder.people}} 人</text>
      </view>
      <view class="info-row">
        <text class="label">预订状态：</text>
        <text class="value {{selectedOrder.reservation_status === 'verified' ? 'success' : 'error'}}">{{ selectedOrder.reservation_status === 'pending' ? '待支付' : selectedOrder.reservation_status === 'paid_full' ? '已预订（' + (selectedOrder.biz_info && selectedOrder.biz_info.name ? '当天11:00前可取消' : selectedOrder.is_lunch ? '当天11:00前可取消' : '当天17:00前可取消') + '）' : selectedOrder.reservation_status === 'paid_deposit' ? '已部分支付' : selectedOrder.reservation_status === 'verified' ? '已就餐' : selectedOrder.reservation_status === 'cancelled' ? '已取消' : selectedOrder.reservation_status === 'auto_verified' ? '超时自动核销' : '其他' }}
          </text>
      </view>
      <view class="info-row">
        <text class="label">报餐时间：</text>
        <text class="value">{{selectedOrder.reservation_time}}</text>
      </view>
      <view class="info-row">
        <text class="label">报餐编码：</text>
        <text class="value">{{selectedOrder.order_item_id}}</text>
      </view>
    </view>
  </view>
</view>

<!-- 在文件末尾添加二维码弹窗 -->
<view class="popup qr-popup" wx:if="{{showQrCode}}" catchtouchmove="preventTouchMove">
  <view class="popup-mask" bindtap="hideQrCode"></view>
  <view class="popup-content">
    <view class="popup-header">
      <text>{{popupTitle}}</text>
      <view class="close-btn" bindtap="hideQrCode">×</view>
    </view>
    <view class="popup-body qr-body">
      <canvas canvas-id="myQrcode" style="width:200px;height:200px;margin:0 auto;display:block;"></canvas>
      <view class="qr-info" wx:if="{{!(selectedOrder.biz_info && selectedOrder.biz_info.name)}}">
        <view class="expire-time">有效期截止：</view>
        <view class="expire-date">{{ selectedOrder.hd_deadline }}</view>
      </view>
      <view class="qr-detail">
        <view class="info-row">
          <text class="label">报餐人员：</text>
          <text class="value">{{selectedOrder.person}}</text>
        </view>
        <view class="info-row">
          <text class="label">就餐时间：</text>
          <text class="value">{{selectedOrder.date}}</text>
        </view>
        <view class="info-row">
          <text class="label">就餐人数：</text>
          <text class="value">{{selectedOrder.people}}人</text>
        </view>
        <view class="info-row">
          <text class="label">预订状态：</text>
          <text class="value {{selectedOrder.reservation_status === 'verified' ? 'success' : 'error'}}">{{ selectedOrder.reservation_status === 'pending' ? '待支付' : selectedOrder.reservation_status === 'paid_full' ? '已预订' : selectedOrder.reservation_status === 'paid_deposit' ? '已部分支付' : selectedOrder.reservation_status === 'verified' ? '已就餐' : selectedOrder.reservation_status === 'cancelled' ? '已取消' : selectedOrder.reservation_status === 'auto_verified' ? '超时自动核销' : '其他' }}（{{selectedOrder.biz_info && selectedOrder.biz_info.name ? '当天11:00前可取消' : selectedOrder.is_lunch ? '当天11:00前可取消' : '当天17:00前可取消'}}）</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 在文件末尾添加取消确认弹窗 -->
<view class="popup cancel-popup" wx:if="{{showCancelConfirm}}" catchtouchmove="preventTouchMove">
  <view class="popup-mask" bindtap="hideCancelConfirm"></view>
  <view class="popup-content">
    <view class="popup-header">
      <text>请确认是否取消</text>
      <view class="close-btn" bindtap="hideCancelConfirm">×</view>
    </view>
    <view class="popup-body cancel-body">
      <view class="cancel-tips">取消成功后，费用将退回对应账户！！</view>
      <view class="confirm-btn" bindtap="confirmCancel" data-order="{{selectedOrder}}">确认</view>
    </view>
  </view>
</view>

<view class="popup filter-popup" wx:if="{{showFilterPopup}}" catchtouchmove="preventTouchMove">
  <view class="popup-mask" bindtap="hideFilterPopup"></view>
  <view class="popup-content">
    <view class="popup-header">
      <text>筛选条件</text>
      <!--<view class="close-btn" bindtap="hideFilterPopup">×</view>-->
    </view>
    <view class="popup-body filter-body">
      <view class="filter-section">
        <view class="section-title">就餐时间</view>
        <view class="date-range">
          <!-- 开始日期 -->
          <view class="date-picker-container start-date">
            <picker mode="date" value="{{filterStartDate}}" start="2020-01-01" end="2030-12-31" bindchange="onStartDateChange">
              <view class="date-picker {{filterStartDate ? 'has-value' : ''}}">
                <text class="{{filterStartDate ? 'date-picker-value' : 'date-picker-placeholder'}} text-left">
                  {{filterStartDate || '开始日期'}}
                </text>
              </view>
            </picker>
          </view>

          <text class="date-separator">至</text>

          <!-- 结束日期 -->
          <view class="date-picker-container end-date">
            <picker mode="date" value="{{filterEndDate}}" start="2020-01-01" end="2030-12-31" bindchange="onEndDateChange">
              <view class="date-picker {{filterEndDate ? 'has-value' : ''}}">
                <text class="{{filterEndDate ? 'date-picker-value' : 'date-picker-placeholder'}} text-right">
                  {{filterEndDate || '结束日期'}}
                </text>
              </view>
            </picker>
          </view>
        </view>
      </view>

      <view class="filter-section">
        <view class="section-title">就餐状态</view>
        <picker mode="selector" range="{{orderStatusOptions}}" value="{{filterOrderStatusIndex}}" bindchange="onOrderStatusChange">
          <view class="status-picker {{filterOrderStatusIndex > 0 ? 'has-value' : ''}}">
            {{orderStatusOptions[filterOrderStatusIndex]}}
          </view>
        </picker>
      </view>

      <view class="popup-footer">
        <!--<button class="btn reset-btn" bindtap="resetFilter">重置</button>-->
        <button class="btn cancel-btn" bindtap="hideFilterPopup">取消</button>
        <button class="btn confirm-btn" bindtap="onFilterConfirm">确认</button>
      </view>
    </view>
  </view>
</view>

<view></view>

<!-- 在文件末尾添加订单详情弹窗 -->
<view class="popup order-detail-popup" wx:if="{{showOrderDetail}}">
  <view class="popup-mask" bindtap="hideOrderDetail"></view>
  <view class="popup-content">
    <view class="popup-header">
      <text>订单详情</text>
      <view class="close-btn" bindtap="hideOrderDetail">×</view>
    </view>

    <!-- 温馨提示 - 移至顶部 -->
    <view class="tips-section">
      <view class="tips-content">
        <text class="tips-text">如需调整预约信息或更换套餐，可直接取消当前订单，重新预约，超时无法取消的，可联系客服电话 18802053864</text>
        <view class="call-button" bindtap="makePhoneCall">拨打</view>
      </view>
    </view>

    <view class="popup-body order-detail-body">
      <!-- 商家信息 -->
      <view class="store-info-section" wx:if="{{selectedOrder.biz_info && selectedOrder.biz_info.name}}">
        <view class="section-title">商家信息</view>
        <view class="store-card">
          <image class="store-avatar" src="{{selectedOrder.biz_info.image || '/images/default-store.png'}}" mode="aspectFill"></image>
          <view class="store-details">
            <view class="store-name">{{selectedOrder.biz_info.name}}</view>
            <view class="store-address">{{selectedOrder.biz_info.address || '地址信息'}}</view>
          </view>
        </view>
      </view>

      <!-- 订单基本信息 -->
      <view class="order-info-section">
        <view class="section-title">订单信息</view>
        <view class="order-info-item">
          <text class="info-label">订单号:</text>
          <text class="info-value">{{selectedOrder.order_no || selectedOrder.order_id}}</text>
        </view>
        <view class="order-info-item">
          <text class="info-label">预约日期:</text>
          <text class="info-value">{{selectedOrder.date}}</text>
        </view>
        <view class="order-info-item">
          <text class="info-label">预约时间:</text>
          <text class="info-value">{{selectedOrder.booking_time || '未设置'}}</text>
        </view>
        <view class="order-info-item">
          <text class="info-label">联系人:</text>
          <text class="info-value">{{selectedOrder.person}}</text>
        </view>
        <view class="order-info-item" wx:if="{{selectedOrder.contact_phone}}">
          <text class="info-label">联系电话:</text>
          <text class="info-value">{{selectedOrder.contact_phone}}</text>
        </view>
        <view class="order-info-item">
          <text class="info-label">用餐人数:</text>
          <text class="info-value">{{selectedOrder.people}}人</text>
        </view>
        <view class="order-info-item">
          <text class="info-label">订单价格:</text>
          <text class="info-value price-value">¥{{selectedOrder.price}}</text>
        </view>
        <view class="order-info-item" wx:if="{{selectedOrder.reservation_time}}">
          <text class="info-label">下单时间:</text>
          <text class="info-value">{{selectedOrder.reservation_time}}</text>
        </view>
        <view class="order-info-item" wx:if="{{selectedOrder.remark}}">
          <text class="info-label">备注:</text>
          <text class="info-value">{{selectedOrder.remark}}</text>
        </view>
      </view>

      <!-- 订单商品列表 -->
      <view class="order-items-section" wx:if="{{selectedOrder.order_items && selectedOrder.order_items.length > 0}}">
        <view class="section-title">订单商品</view>
        <view class="order-item" wx:for="{{selectedOrder.order_items}}" wx:key="dish_id">
          <view class="order-item-left">
            <image class="item-image" src="{{item.image ? (item.image.indexOf('http') === 0 ? item.image : baseUrlHost + item.image) : '/images/default-dish.png'}}" mode="aspectFill"></image>
            <view class="item-info">
              <text class="item-name">{{item.name}}</text>
              <text class="item-price">¥{{item.price}}</text>
            </view>
          </view>
          <view class="item-right">
            <view class="item-quantity">x{{item.quantity}}</view>
            <view class="item-subtotal">¥{{item.price * item.quantity}}</view>
          </view>
        </view>
      </view>

      <!-- 支付信息 -->
      <view class="payment-info-section">
        <view class="section-title">支付信息</view>
        <!--
        <view class="payment-info-item">
          <text class="info-label">支付状态:</text>
          <text class="info-value {{selectedOrder.reservation_status === 'verified' ? 'success' : 'error'}}">
            {{ selectedOrder.isFullyPaid ? '已结账' :
               (selectedOrder.reservation_status === 'pending' ? '待支付' :
                selectedOrder.reservation_status === 'paid' ? '已支付' :
                selectedOrder.reservation_status === 'paid_full' ? (selectedOrder.is_modified ? '已结账' : '已支付，未结账') :
                selectedOrder.reservation_status === 'paid_deposit' ? '已部分支付' :
                selectedOrder.reservation_status === 'verified' ? '已就餐' :
                selectedOrder.reservation_status === 'cancelled' ? '已取消' :
                selectedOrder.reservation_status === 'auto_verified' ? '超时自动结账' : '其他') }}
          </text>
        </view>
        -->
        <view class="payment-info-item" wx:if="{{selectedOrder.payment_time}}">
          <text class="info-label">下单时间:</text>
          <text class="info-value">{{selectedOrder.payment_time}}</text>
        </view>
      </view>

      <!-- 结账按钮 - 只在商务餐订单中显示，且商品总金额不等于订单价格 -->
      <view class="checkout-section" wx:if="{{selectedOrder.biz_info && selectedOrder.biz_info.name && selectedOrder.shouldShowCheckout}}">
        <button class="checkout-btn" bindtap="goToCheckout" data-order-id="{{selectedOrder.order_id}}">
          结账
        </button>
        <view class="order-detail-bottom-spacer"></view>
      </view>
    </view>
  </view>
</view>