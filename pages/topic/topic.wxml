<view class="page-container">
  
  
  <view class="meal-options">
    <view class="meal-card" bindtap="navigateToEmployeeBooking" data-type="self">
      <view class="meal-icon-container">
        <text class="meal-icon">🍽️</text>
      </view>
      <view class="meal-content">
        <text class="meal-title">自助餐</text>
        <text class="red-tip">10:00前预订当天用餐</text>
      </view>
      <view class="arrow">›</view>
    </view>
    
    <!--<view class="meal-card" bindtap="showMealTypeDialog" data-type="business">-->
    <view class="meal-card" bindtap="navigateToBusinessBooking" data-type="business">
      <view class="meal-icon-container business">
        <text class="meal-icon">🍲</text>
      </view>
      <view class="meal-content">
        <text class="meal-title">商务餐</text>
        <text class="red-tip">09:30前预订当天用餐</text>
      </view>
      <view class="arrow">›</view>
    </view>

    <!--
    <view class="meal-card" bindtap="selectMealType" data-type="vegan">
      <view class="meal-icon-container vegan">
        <text class="meal-icon">🌱</text>
      </view>
      <view class="meal-content">
        <text class="meal-title">菜品点餐</text>
        <text class="meal-desc">单点特色素食佳肴</text>
      </view>
      <view class="arrow">›</view>
    </view> -->
    <!--
    <view class="meal-card" bindtap="selectMealType" data-type="onsite">
      <view class="meal-icon-container onsite">
        <text class="meal-icon">🥙</text>
      </view>
      <view class="meal-content">
        <text class="meal-title">临时点餐</text>
        <text class="red-tip">提供到店即时可用餐服务</text>
      </view>
      <view class="arrow">›</view>
    </view>
    -->
  </view>
</view>

<!-- 移除整个弹窗组件 -->