/* 配送时间选择器样式 */
.time-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 300;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}

.modal-content {
  position: relative;
  background: #fff;
  border-radius: 24rpx;
  width: 90%;
  max-height: 80%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.2);
  animation: slide-up 0.3s ease;
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 标题栏 */
.modal-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #ff4a4a, #ff7676);
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(255, 74, 74, 0.2);
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

/* 餐型选择 */
.meal-type-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.meal-type-tabs {
  display: flex;
  gap: 20rpx;
}

.meal-type-tab {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  border: 2rpx solid #eee;
  background: #fff;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.meal-type-tab.active {
  border-color: #ff4a4a;
  background: rgba(255, 74, 74, 0.1);
  color: #ff4a4a;
  font-weight: 600;
}

/* 区块标题 */
.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

/* 日期选择 */
.date-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.date-scroll {
  width: 100%;
  white-space: nowrap;
}

.date-list {
  display: inline-flex;
  gap: 16rpx;
}

.date-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
  border: 2rpx solid #eee;
  background: #fff;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.date-item.selected {
  border-color: #ff4a4a;
  background: rgba(255, 74, 74, 0.1);
}

.date-text {
  font-size: 26rpx;
  color: #666;
}

.date-item.selected .date-text {
  color: #ff4a4a;
  font-weight: 600;
}

/* 时间段选择 */
.time-slot-section {
  flex: 1;
  padding: 30rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.time-slot-scroll {
  flex: 1;
  height: 100%;
}

.time-slot-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.time-slot-item {
  padding: 24rpx;
  border-radius: 12rpx;
  border: 2rpx solid #eee;
  background: #fff;
  transition: all 0.3s ease;
}

.time-slot-item.selected {
  border-color: #ff4a4a;
  background: rgba(255, 74, 74, 0.05);
}

.time-slot-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.time-slot-time {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.time-slot-item.selected .time-slot-time {
  color: #ff4a4a;
}

.time-slot-name {
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.time-slot-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.time-slot-deadline {
  font-size: 22rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

