Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 配送配置数据
    deliveryConfig: {
      type: Object,
      value: null
    },
    // 是否显示选择器
    visible: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    tag: 'delivery_lunch', // 'delivery_lunch' or 'delivery_dinner'
    dates: [], // 可选日期列表
    selectedDate: null, // 选中的日期
    timeSlots: [], // 当前日期的时间段列表
    selectedTimeSlot: null, // 选中的时间段
    allTimeSlots: {} // 所有时间段数据，按日期分组
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 转换日期字符串为 iOS 兼容格式
     * 将 "YYYY-MM-DD HH:mm" 转换为 "YYYY/MM/DD HH:mm"
     */
    convertDateForIOS(dateStr) {
      if (!dateStr) return dateStr;
      // 将所有的 "-" 替换为 "/"，iOS 支持这种格式
      return dateStr.replace(/-/g, '/');
    },

    /**
     * 切换餐型（午餐/晚餐）
     */
    toggleMealType(e) {
      const tag = e.currentTarget.dataset.type;
      this.setData({
        tag,
        selectedDate: null,
        selectedTimeSlot: null,
        timeSlots: []
      });
      this.processDeliveryConfig();
    },

    /**
     * 选择日期
     */
    selectDate(e) {
      const date = e.currentTarget.dataset.date;
      const timeSlots = this.data.allTimeSlots[date] || [];
      
      this.setData({
        selectedDate: date,
        timeSlots,
        selectedTimeSlot: null
      });
    },

    /**
     * 选择时间段
     */
    selectTimeSlot(e) {
      const slot = e.currentTarget.dataset.slot;

      this.setData({
        selectedTimeSlot: slot
      });

      // 格式化日期显示（使用 iOS 兼容的日期格式）
      const dateObj = new Date(this.convertDateForIOS(this.data.selectedDate));
      const month = dateObj.getMonth() + 1;
      const day = dateObj.getDate();
      const dateDisplay = `${month}月${day}日`;

      // 触发选择完成事件
      this.triggerEvent('select', {
        tag: this.data.tag,
        date: this.data.selectedDate,
        dateDisplay: dateDisplay, // 添加格式化的日期显示
        timeSlot: {
          ...slot,
          dateDisplay: dateDisplay // 也添加到 timeSlot 中
        }
      });

      // 关闭选择器
      this.triggerEvent('close');
    },

    /**
     * 关闭选择器
     */
    close() {
      this.triggerEvent('close');
    },

    /**
     * 处理配送配置数据
     */
    processDeliveryConfig() {
      const { deliveryConfig, tag } = this.data;

      if (!deliveryConfig) {
        return;
      }

      // 根据tag获取对应的配置
      const mealConfig = deliveryConfig[tag];

      if (!mealConfig || !mealConfig.rules || mealConfig.rules.length === 0) {
        this.setData({
          dates: [],
          allTimeSlots: {},
          timeSlots: []
        });
        return;
      }

      // 收集所有时间段数据
      const allTimeSlots = {};
      const dateSet = new Set();

      // 遍历所有规则和规则项
      mealConfig.rules.forEach(rule => {
        if (rule.rule_items && rule.rule_items.length > 0) {
          rule.rule_items.forEach(ruleItem => {
            if (ruleItem.rule_items_times && ruleItem.rule_items_times.length > 0) {
              ruleItem.rule_items_times.forEach(timeItem => {
                // 提取日期（格式：YYYY-MM-DD）
                const date = timeItem.dining_start_time.split(' ')[0];
                dateSet.add(date);

                // 初始化日期对应的时间段数组
                if (!allTimeSlots[date]) {
                  allTimeSlots[date] = [];
                }

                // 添加时间段信息
                allTimeSlots[date].push({
                  ...timeItem,
                  ruleItemId: ruleItem.id,
                  ruleItemName: ruleItem.name,
                  ruleId: rule.rule_id,
                  ruleName: rule.rule_name,
                  // 格式化显示的时间段文本
                  displayTime: this.formatTimeSlot(timeItem.dining_start_time, timeItem.dining_end_time)
                });
              });
            }
          });
        }
      });

      // 对每个日期的时间段按开始时间排序
      Object.keys(allTimeSlots).forEach(date => {
        allTimeSlots[date].sort((a, b) => {
          // 使用 iOS 兼容的日期格式
          return new Date(this.convertDateForIOS(a.dining_start_time)) -
                 new Date(this.convertDateForIOS(b.dining_start_time));
        });
      });

      // 转换日期集合为数组并排序
      const dates = Array.from(dateSet).sort();

      // 格式化日期显示
      const formattedDates = dates.map(date => ({
        value: date,
        display: this.formatDate(date)
      }));

      this.setData({
        dates: formattedDates,
        allTimeSlots,
        selectedDate: null,
        timeSlots: [],
        selectedTimeSlot: null
      });
    },

    /**
     * 格式化日期显示（YYYY-MM-DD -> M月D日）
     */
    formatDate(dateStr) {
      // 使用 iOS 兼容的日期格式
      const date = new Date(this.convertDateForIOS(dateStr));
      const month = date.getMonth() + 1;
      const day = date.getDate();

      // 获取星期
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const weekday = weekdays[date.getDay()];

      return `${month}月${day}日 ${weekday}`;
    },

    /**
     * 格式化时间段显示
     */
    formatTimeSlot(startTime, endTime) {
      // 提取时间部分（HH:MM）
      const start = startTime.split(' ')[1].substring(0, 5);
      const end = endTime.split(' ')[1].substring(0, 5);
      return `${start}～${end}`;
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
    },
    ready() {
      // 组件在视图层布局完成后执行
      if (this.data.deliveryConfig) {
        this.processDeliveryConfig();
      }
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'deliveryConfig': function(newConfig) {
      if (newConfig) {
        this.processDeliveryConfig();
      }
    },
    'visible': function(visible) {
      if (visible && this.data.deliveryConfig) {
        this.processDeliveryConfig();
      }
    }
  }
});

