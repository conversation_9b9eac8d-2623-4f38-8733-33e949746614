<!-- 配送时间选择器 -->
<view class="time-selector-modal" wx:if="{{visible}}">
  <view class="modal-mask" bindtap="close"></view>
  <view class="modal-content">
    <!-- 标题栏 -->
    <view class="modal-header">
      <text class="modal-title">选择配送时间</text>
      <view class="modal-close" bindtap="close">×</view>
    </view>

    <!-- 餐型选择 -->
    <view class="meal-type-section">
      <view class="meal-type-tabs">
        <view
          class="meal-type-tab {{tag === 'delivery_lunch' ? 'active' : ''}}"
          bindtap="toggleMealType"
          data-type="delivery_lunch">
          <text>午餐</text>
        </view>
        <view
          class="meal-type-tab {{tag === 'delivery_dinner' ? 'active' : ''}}"
          bindtap="toggleMealType"
          data-type="delivery_dinner">
          <text>晚餐</text>
        </view>
      </view>
    </view>

    <!-- 日期选择 -->
    <view class="date-section" wx:if="{{dates.length > 0}}">
      <view class="section-title">选择日期</view>
      <scroll-view scroll-x="true" class="date-scroll">
        <view class="date-list">
          <view 
            class="date-item {{selectedDate === date.value ? 'selected' : ''}}"
            wx:for="{{dates}}"
            wx:key="value"
            wx:for-item="date"
            bindtap="selectDate"
            data-date="{{date.value}}">
            <text class="date-text">{{date.display}}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 时间段选择 -->
    <view class="time-slot-section" wx:if="{{selectedDate && timeSlots.length > 0}}">
      <view class="section-title">选择时间段</view>
      <scroll-view scroll-y="true" class="time-slot-scroll">
        <view class="time-slot-list">
          <view 
            class="time-slot-item {{selectedTimeSlot && selectedTimeSlot.dining_start_time === slot.dining_start_time ? 'selected' : ''}}"
            wx:for="{{timeSlots}}"
            wx:key="dining_start_time"
            wx:for-item="slot"
            bindtap="selectTimeSlot"
            data-slot="{{slot}}">
            <view class="time-slot-main">
              <text class="time-slot-time">{{slot.displayTime}}</text>
              <text class="time-slot-name">{{slot.ruleItemName}}</text>
            </view>
            <view class="time-slot-info">
              <text class="time-slot-deadline">截单时间: {{slot.order_deadline}}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 空状态提示 -->
    <view class="empty-state" wx:if="{{dates.length === 0}}">
      <text class="empty-text">暂无可选{{tag === 'delivery_lunch' ? '午餐' : '晚餐'}}时间</text>
    </view>

    <view class="empty-state" wx:if="{{dates.length > 0 && !selectedDate}}">
      <text class="empty-text">请先选择日期</text>
    </view>

    <view class="empty-state" wx:if="{{selectedDate && timeSlots.length === 0}}">
      <text class="empty-text">该日期暂无可选时间段</text>
    </view>
  </view>
</view>

