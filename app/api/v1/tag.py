from typing import List, Dict, Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.product import product_dao
from app.dao.rule import rule_dao
from app.dao.tag import tag_dao
from app.schemas.tag import TagCreate, TagUpdate, TagResponse, MultiProductBinding, MultiRuleBinding
from app.service.tag import tag_service

router = APIRouter()


@router.post("/", response_model=TagResponse, status_code=status.HTTP_201_CREATED)
def create_tag(tag: TagCreate, db: Session = Depends(get_db)):
    """创建标签"""
    return tag_dao.create(session=db, tag=tag)


@router.get("/{tag_id}", response_model=TagResponse)
def read_tag(tag_id: int, db: Session = Depends(get_db)):
    """获取标签详情"""
    db_tag = tag_dao.get(session=db, tag_id=tag_id)
    if db_tag is None:
        raise HTTPException(status_code=404, detail="标签不存在")
    return db_tag


@router.get("/by-name/{name}", response_model=TagResponse)
def read_tag_by_name(name: str, db: Session = Depends(get_db)):
    """根据名称获取标签"""
    db_tag = tag_dao.get_by_name(session=db, name=name)
    if db_tag is None:
        raise HTTPException(status_code=404, detail="标签不存在")
    return db_tag


@router.get("/", response_model=List[TagResponse])
def read_tags(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取标签列表"""
    return tag_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/products/{product_id}/", response_model=List[TagResponse])
def read_product_tags(product_id: int, db: Session = Depends(get_db)):
    """获取产品相关的标签列表"""
    product = product_dao.get(session=db, product_id=product_id)
    if product is None:
        raise HTTPException(status_code=404, detail="产品不存在")
    return tag_dao.get_by_product(session=db, product_id=product_id)


@router.put("/{tag_id}", response_model=TagResponse)
def update_tag(tag_id: int, tag: TagUpdate, db: Session = Depends(get_db)):
    """更新标签信息"""
    db_tag = tag_dao.update(session=db, tag_id=tag_id, tag=tag)
    if db_tag is None:
        raise HTTPException(status_code=404, detail="标签不存在")
    return db_tag


@router.delete("/{tag_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_tag(tag_id: int, db: Session = Depends(get_db)):
    """删除标签"""
    success = tag_dao.delete(session=db, tag_id=tag_id)
    if not success:
        raise HTTPException(status_code=404, detail="标签不存在")
    return {"message": "标签删除成功"}


@router.post("/{tag_id}/bind/products", response_model=TagResponse)
def bind_products_to_tag(tag_id: int, binding: MultiProductBinding, db: Session = Depends(get_db)):
    """将标签同时绑定到多个产品"""
    # 验证标签存在
    tag = tag_dao.get(session=db, tag_id=tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="标签不存在")

    # 验证至少有一个产品
    if not binding.product_ids:
        raise HTTPException(status_code=400, detail="必须提供至少一个产品ID")

    # 批量绑定
    tag = tag_dao.bind_products(session=db, tag_id=tag_id, product_ids=binding.product_ids)
    if not tag:
        raise HTTPException(status_code=404, detail="没有找到有效的产品")

    return tag


@router.post("/{tag_id}/unbind/products", response_model=TagResponse)
def unbind_products_from_tag(tag_id: int, binding: MultiProductBinding, db: Session = Depends(get_db)):
    """将标签同时从多个产品解绑"""
    # 验证标签存在
    tag = tag_dao.get(session=db, tag_id=tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="标签不存在")

    # 验证至少有一个产品
    if not binding.product_ids:
        raise HTTPException(status_code=400, detail="必须提供至少一个产品ID")

    # 批量解绑
    tag = tag_dao.unbind_products(session=db, tag_id=tag_id, product_ids=binding.product_ids)

    return tag


@router.post("/{tag_id}/bind/rules", response_model=TagResponse)
def bind_rules_to_tag(tag_id: int, binding: MultiRuleBinding, db: Session = Depends(get_db)):
    """将标签同时绑定到多个规则"""
    # 验证标签存在
    tag = tag_dao.get(session=db, tag_id=tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="标签不存在")

    # 验证至少有一个规则
    if not binding.rule_ids:
        raise HTTPException(status_code=400, detail="必须提供至少一个规则ID")

    # 批量绑定
    tag = tag_dao.bind_rules(session=db, tag_id=tag_id, rule_ids=binding.rule_ids)
    if not tag:
        raise HTTPException(status_code=404, detail="没有找到有效的规则")

    return tag


@router.post("/{tag_id}/unbind/rules", response_model=TagResponse)
def unbind_rules_from_tag(tag_id: int, binding: MultiRuleBinding, db: Session = Depends(get_db)):
    """将标签同时从多个规则解绑"""
    # 验证标签存在
    tag = tag_dao.get(session=db, tag_id=tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="标签不存在")

    # 验证至少有一个规则
    if not binding.rule_ids:
        raise HTTPException(status_code=400, detail="必须提供至少一个规则ID")

    # 批量解绑
    tag = tag_dao.unbind_rules(session=db, tag_id=tag_id, rule_ids=binding.rule_ids)

    return tag


@router.get("/by-name/{tag_name}/products")
def get_product_ids_by_tag_name(tag_name: str, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """根据标签名称获取产品ID列表"""
    product_ids = tag_service.get_product_ids_by_tag_name(db, tag_name)

    return {
        "code": 200,
        "message": "success",
        "data": {
            "tag_name": tag_name,
            "product_ids": product_ids
        }
    }


@router.get("/by-name/{tag_name}/rules")
def get_rule_ids_by_tag_name(tag_name: str, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """根据标签名称获取规则ID列表"""
    rule_ids = tag_service.get_rule_ids_by_tag_name(db, tag_name)

    return {
        "code": 200,
        "message": "success",
        "data": {
            "tag_name": tag_name,
            "rule_ids": rule_ids
        }
    }


@router.get("/by-name/{tag_name}/rules/with-items")
def get_rules_with_items_by_tag_name(tag_name: str, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """根据标签名称获取时间规则及其规则项"""
    rules = tag_service.get_rules_with_items_by_tag_name(db, tag_name)

    return {
        "code": 200,
        "message": "success",
        "data": {
            "tag_name": tag_name,
            "rules": rules
        }
    }


@router.post("/by-name/{tag_name}/time-periods")
def generate_time_periods_by_tag(
    tag_name: str,
    start_date: str = Query(..., description="开始日期，格式：YYYY-MM-DD"),
    end_date: str = Query(..., description="结束日期，格式：YYYY-MM-DD"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """根据标签名称和时间规则生成日期时间段

    示例返回数据：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "tag_name": "午餐",
            "start_date": "2024-01-01",
            "end_date": "2024-01-07",
            "periods": [
                {
                    "rule_id": 1,
                    "rule_item_id": 1,
                    "rule_item_name": "工作日午餐",
                    "rule_item_alias": "lunch",
                    "start_time": "2024-01-01 11:30:00",
                    "end_time": "2024-01-01 13:30:00",
                    "order_deadline": 60,
                    "cancellation_deadline": 30,
                    "quantity": 100,
                    "meal_type": "lunch"
                }
            ]
        }
    }
    ```
    """
    try:
        # 解析日期
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")

        if start_dt > end_dt:
            raise HTTPException(status_code=400, detail="开始日期不能晚于结束日期")

        # 生成时间段
        periods = tag_service.generate_time_periods_by_tag(db, tag_name, start_dt, end_dt)

        return {
            "code": 200,
            "message": "success",
            "data": {
                "tag_name": tag_name,
                "start_date": start_date,
                "end_date": end_date,
                "periods": periods
            }
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"日期格式错误: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成时间段失败: {str(e)}")
