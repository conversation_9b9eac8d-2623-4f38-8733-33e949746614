from fastapi import APIRouter
from .enterprise import router as enterprise_router
from .article import router as article_router
from .message import router as message_router
from .admin import router as admin_router
from .config import router as config_router
from .onsite import router as onsite_router
from .biz_dinner import router as biz_dinner_router
from .menu import router as menu_router
from .reservation import router as reservation_router
from .order import router as order_router
from .split_payment import router as split_payment_router
from .payment import router as payment_router
from .auth import router as auth_router
from .account import router as account_router
from .coupon import router as coupon_router
from .shopping import router as shopping_router
from .delivery import router as delivery_router
from .user import router as user_router
from .delivery import router as delivery_router

router = APIRouter()

router.include_router(enterprise_router, prefix="/enterprise", tags=["微信小程序-企业管理"])
router.include_router(article_router, prefix="/article", tags=["微信小程序-文章内容"])
router.include_router(message_router, prefix="/message", tags=["微信小程序-订阅消息"])
router.include_router(admin_router, prefix="/admin", tags=["微信小程序-管理员"])
router.include_router(config_router, prefix="/config", tags=["微信小程序-配置"])

router.include_router(auth_router, tags=["微信小程序-认证"])
router.include_router(account_router, tags=["微信小程序-账户"])
router.include_router(reservation_router, tags=["微信小程序-预约"])
router.include_router(order_router, tags=["微信小程序-订单"])
router.include_router(menu_router, tags=["微信小程序-菜单"])
router.include_router(biz_dinner_router, tags=["微信小程序-商务餐"])
router.include_router(onsite_router, tags=["微信小程序-现场点餐"])
router.include_router(payment_router, tags=["微信小程序-支付"])
router.include_router(split_payment_router, tags=["微信小程序-分账"])
router.include_router(coupon_router, prefix="/coupon", tags=["微信小程序-优惠券"])
router.include_router(shopping_router, tags=["微信小程序-购物订单"])
router.include_router(delivery_router, tags=["微信小程序-外卖订单"])
router.include_router(user_router, prefix="/user", tags=["微信小程序-用户"])
router.include_router(delivery_router, tags=["微信小程序-外卖"])




__all__ = ["router"]
