from fastapi import APIRouter
from .admin import router as admin_router
from .scan import router as scan_router
from .statistic import router as statistic_router
from .delivery import router as delivery_router

router = APIRouter()

router.include_router(admin_router)
router.include_router(scan_router)
router.include_router(statistic_router, prefix="/statistic")
router.include_router(delivery_router, prefix="/delivery")

__all__ = ["router"]
