# 提供小程序管理员外卖订单查询模块API

import json
from datetime import datetime
from typing import Dict, Any, Optional, List

from fastapi import Depends, HTTPException, Header, APIRouter, Query, Body
from sqlalchemy.orm import Session, joinedload

from app.api.v1.wechat_mini_app.admin.common import verify_admin_permission
from app.core.deps import get_db
from app.models.order import DeliveryOrder, OrderType, OrderStatus, PaymentStatus, OrderItem
from app.models.user import User
from app.models.product import Product
from app.schemas.order import PrintDeliveryOrdersRequest
from app.service.feieyun import feieyun_service
from app.service.fengniao import FengniaoClient
from app.utils.logger import logger

router = APIRouter()


@router.get("/delivery_orders")
async def get_delivery_orders(
        token: Optional[str] = Header(None),
        start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
        end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
        order_status: Optional[str] = Query(None, description="订单状态"),
        payment_status: Optional[str] = Query(None, description="支付状态"),
        user_phone: Optional[str] = Query(None, description="用户手机号"),
        order_no: Optional[str] = Query(None, description="订单号"),
        page: Optional[int] = Query(1, description="页码"),
        page_size: Optional[int] = Query(10, description="每页记录数"),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    小程序管理员获取外卖订单列表

    需要管理员权限（miniapp:manage）

    查询条件：
    - 日期范围（按订单创建时间）
    - 订单状态
    - 支付状态
    - 用户手机号
    - 订单号
    - 分页参数

    返回结果包含：
    - 订单基本信息（订单号、状态、金额等）
    - 用户信息（姓名、手机号等）
    - 配送信息（配送地址、配送时间、配送费等）
    - 订单项信息（商品详情）
    """
    logger.info(f"小程序管理员请求外卖订单列表，token: {token[:10] if token else 'None'}...")

    # 验证管理员权限
    if not verify_admin_permission(token, db):
        logger.warning("用户无管理员权限或token无效")
        return {
            "code": 403,
            "message": "无权限访问",
            "data": None
        }

    try:
        # 构建查询
        query = db.query(DeliveryOrder).join(User, DeliveryOrder.user_id == User.id)

        # 添加日期范围过滤
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
                query = query.filter(DeliveryOrder.delivery_time >= start_datetime)
                logger.info(f"添加开始日期过滤: {start_datetime}")
            except ValueError:
                logger.error(f"开始日期格式错误: {start_date}")
                return {
                    "code": 400,
                    "message": "无效的开始日期格式，请使用 YYYY-MM-DD",
                    "data": None
                }

        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
                # 设置为当天的23:59:59
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                query = query.filter(DeliveryOrder.delivery_time <= end_datetime)
                logger.info(f"添加结束日期过滤: {end_datetime}")
            except ValueError:
                logger.error(f"结束日期格式错误: {end_date}")
                return {
                    "code": 400,
                    "message": "无效的结束日期格式，请使用 YYYY-MM-DD",
                    "data": None
                }

        # 添加订单状态过滤
        if order_status:
            try:
                status_enum = OrderStatus(order_status)
                query = query.filter(DeliveryOrder.status == status_enum)
                logger.info(f"添加订单状态过滤: {order_status}")
            except ValueError:
                logger.error(f"无效的订单状态: {order_status}")
                return {
                    "code": 400,
                    "message": f"无效的订单状态: {order_status}",
                    "data": None
                }

        # 添加支付状态过滤
        if payment_status:
            try:
                payment_status_enum = PaymentStatus(payment_status)
                query = query.filter(DeliveryOrder.payment_status == payment_status_enum)
                logger.info(f"添加支付状态过滤: {payment_status}")
            except ValueError:
                logger.error(f"无效的支付状态: {payment_status}")
                return {
                    "code": 400,
                    "message": f"无效的支付状态: {payment_status}",
                    "data": None
                }

        # 添加用户手机号过滤
        if user_phone:
            query = query.filter(User.phone.like(f"%{user_phone}%"))
            logger.info(f"添加用户手机号过滤: {user_phone}")

        # 添加订单号过滤
        if order_no:
            query = query.filter(DeliveryOrder.order_no.like(f"%{order_no}%"))
            logger.info(f"添加订单号过滤: {order_no}")

        # 获取总数
        total_count = query.count()

        # 添加排序和分页
        query = query.order_by(DeliveryOrder.created_at.desc())

        # 计算分页
        offset = (page - 1) * page_size
        orders = query.offset(offset).limit(page_size).options(
            joinedload(DeliveryOrder.user),
            joinedload(DeliveryOrder.items).joinedload(OrderItem.product)
        ).all()

        logger.info(f"查询到 {len(orders)} 条外卖订单记录，总数: {total_count}")

        # 构建返回数据
        order_list = []
        for order in orders:
            # 构建订单项信息
            order_items = []
            for item in order.items:
                order_items.append({
                    "id": item.id,
                    "product_id": item.product_id,
                    "product_name": item.product.name if item.product else "未知商品",
                    "quantity": item.quantity,
                    "price": item.price,
                    "subtotal": item.subtotal,
                    "final_price": item.final_price,
                    "payable_amount": item.payable_amount,
                    "pricing_remark": item.pricing_remark or ""
                })

            # 构建订单信息
            order_data = {
                "id": order.id,
                "order_no": order.order_no,
                "status": order.status.value,
                "payment_status": order.payment_status.value,
                "payment_method": order.payment_method.value if order.payment_method else None,
                "total_amount": order.total_amount,
                "payable_amount": order.payable_amount,
                "actual_amount_paid": order.actual_amount_paid,
                "discount_amount": order.discount_amount,
                "created_at": order.created_at.strftime("%Y-%m-%d %H:%M:%S") if order.created_at else None,
                "updated_at": order.updated_at.strftime("%Y-%m-%d %H:%M:%S") if order.updated_at else None,
                "payment_time": order.payment_time.strftime("%Y-%m-%d %H:%M:%S") if order.payment_time else None,

                # 用户信息
                "user_id": order.user_id,
                "user_name": order.user.real_name if order.user and hasattr(order.user, 'real_name') and order.user.real_name else "未知用户",
                "user_phone": order.user.phone if order.user and hasattr(order.user, 'phone') else "未知手机号",
                "user_nick_name": order.user.nickname if order.user and hasattr(order.user, 'nickname') else "未知昵称",

                # 配送信息
                "delivery_address": order.delivery_address,
                "delivery_address_raw": order.delivery_address_raw,
                "delivery_time": order.delivery_time.strftime("%Y-%m-%d %H:%M:%S") if order.delivery_time else None,
                "delivery_time_raw": order.delivery_time_raw,
                "delivery_fee": order.delivery_fee,
                "fengniao_order_id": order.fengniao_order_id,
                "fengniao_status": order.fengniao_status,

                # 订单项
                "items": order_items
            }

            order_list.append(order_data)

        data = {
            "list": order_list,
            "total": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": (total_count + page_size - 1) // page_size
        }

        logger.info(f"小程序管理员外卖订单查询成功，返回 {len(order_list)} 条记录")

        return {
            "code": 200,
            "message": "success",
            "data": data
        }

    except Exception as e:
        logger.error(f"小程序管理员外卖订单查询失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return {
            "code": 500,
            "message": f"查询失败: {str(e)}",
            "data": None
        }


@router.post("/print_delivery_orders")
async def print_delivery_orders(
        request: Optional[PrintDeliveryOrdersRequest] = Body(None),
        token: Optional[str] = Header(None),
        start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
        end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
        order_id: Optional[int] = Query(None, description="单个订单ID"),
        order_ids: Optional[List[int]] = Query(None, description="订单ID列表"),
        sn: Optional[str] = Query(None, description="打印机编号（可选，默认使用配置）"),
        times: Optional[int] = Query(1, description="打印次数，默认1次"),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    小程序管理员打印外卖订单

    需要管理员权限（miniapp:manage）

    支持两种方式传参：
    1. Query 参数方式
    2. POST Body JSON 方式

    查询条件（三选一）：
    - 日期范围（start_date 和/或 end_date）
    - 单个订单ID（order_id）
    - 订单ID列表（order_ids）

    其他参数：
    - sn: 打印机编号（可选，默认使用配置中的打印机）
    - times: 打印次数（默认1次）

    返回结果包含：
    - 打印成功的订单数量
    - 每个订单的打印结果详情
    """
    logger.info(f"小程序管理员请求打印外卖订单，token: {token[:10] if token else 'None'}...")

    # 验证管理员权限
    if not verify_admin_permission(token, db):
        logger.warning("用户无管理员权限或token无效")
        raise HTTPException(
            status_code=403,
            detail={"message": "无权限访问", "status": 403}
        )

    # 合并 Body 和 Query 参数（Query 参数优先级更高）
    params = {}

    # 首先从 request body 获取参数
    if request:
        request_params = request.model_dump(exclude_none=True)
        logger.info(f"从 request body 获取参数: {request_params}")
        params.update(request_params)

    # Query 参数覆盖 Body 参数
    if start_date is not None:
        params['start_date'] = start_date
    if end_date is not None:
        params['end_date'] = end_date
    if order_id is not None:
        params['order_id'] = order_id
    if order_ids is not None:
        params['order_ids'] = order_ids
    if sn is not None:
        params['sn'] = sn
    if times is not None:
        params['times'] = times

    logger.info(f"最终使用的参数: {params}")

    try:
        # 调用飞鹅云打印服务
        result = feieyun_service.print_delivery_orders(
            db=db,
            start_date=params.get('start_date'),
            end_date=params.get('end_date'),
            order_id=params.get('order_id'),
            order_ids=params.get('order_ids'),
            sn=params.get('sn'),
            times=params.get('times', 1)
        )

        logger.info(f"打印结果: {result}")

        # 根据打印结果返回相应的状态码
        if result.get('success'):
            return {
                "code": 200,
                "message": result.get('message', '打印成功'),
                "data": {
                    "printed_count": result.get('printed_count', 0),
                    "total_count": result.get('total_count', 0),
                    "print_results": result.get('print_results', [])
                }
            }
        else:
            return {
                "code": 400,
                "message": result.get('message', '打印失败'),
                "data": {
                    "printed_count": result.get('printed_count', 0),
                    "total_count": result.get('total_count', 0),
                    "print_results": result.get('print_results', [])
                }
            }

    except Exception as e:
        logger.error(f"小程序管理员打印外卖订单失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail={"message": f"打印失败: {str(e)}", "status": 500}
        )


@router.get("/order_detail")
async def get_order_detail(
        token: Optional[str] = Header(None),
        partner_order_code: Optional[str] = Query(None, description="外部订单号（我们系统的订单号）"),
        order_id: Optional[int] = Query(None, description="蜂鸟订单ID"),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    查询蜂鸟订单详情接口

    需要管理员权限（miniapp:manage）

    参数（至少提供一个）：
    - partner_order_code: 外部订单号（我们系统的订单号）
    - order_id: 蜂鸟订单ID

    返回蜂鸟订单的详细信息，包括：
    - 订单基本信息（订单号、状态、距离等）
    - 配送费用信息（原始费用、实际费用、小费等）
    - 配送员信息（姓名、电话、ID等）
    - 运单事件节点信息
    - 价格详情
    - 异常、投诉、索赔信息（如有）
    """
    logger.info(f"小程序管理员请求查询蜂鸟订单详情，token: {token[:10] if token else 'None'}...")

    # 验证管理员权限
    if not verify_admin_permission(token, db):
        logger.warning("用户无管理员权限或token无效")
        return {
            "code": 403,
            "message": "无权限访问",
            "data": None
        }

    # 验证参数：至少提供一个
    if not partner_order_code and not order_id:
        logger.warning("未提供订单号或蜂鸟订单ID")
        return {
            "code": 400,
            "message": "请提供订单号(partner_order_code)或蜂鸟订单ID(order_id)中的至少一个",
            "data": None
        }

    try:
        # 构建查询参数
        query_params = {}
        if partner_order_code:
            query_params["partner_order_code"] = partner_order_code
        if order_id:
            query_params["order_id"] = order_id

        logger.info(f"查询参数: {query_params}")

        # 调用蜂鸟API
        fengniao_client = FengniaoClient()
        result = fengniao_client.get_order_detail(query_params)

        logger.info(f"蜂鸟API响应: {result}")

        # 检查蜂鸟API调用结果
        if result.get("code") != "200":
            logger.error(f"蜂鸟订单详情查询失败: {result.get('msg', '未知错误')}")
            return {
                "code": 500,
                "message": f"查询失败: {result.get('msg', '未知错误')}",
                "data": None
            }

        # 解析业务数据
        business_data = result.get('business_data')
        if business_data:
            try:
                if isinstance(business_data, str):
                    business_data = json.loads(business_data)

                logger.info(f"订单详情查询成功，订单状态: {business_data.get('order_status')}")

                return {
                    "code": 200,
                    "message": "success",
                    "data": business_data
                }
            except json.JSONDecodeError as e:
                logger.error(f"解析业务数据失败: {e}")
                return {
                    "code": 500,
                    "message": f"解析响应数据失败: {str(e)}",
                    "data": None
                }
        else:
            logger.warning("蜂鸟API未返回业务数据")
            return {
                "code": 500,
                "message": "未获取到订单详情数据",
                "data": None
            }

    except Exception as e:
        logger.error(f"查询蜂鸟订单详情失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return {
            "code": 500,
            "message": f"查询失败: {str(e)}",
            "data": None
        }


@router.get("/rider_info")
async def get_rider_info(
        token: Optional[str] = Header(None),
        partner_order_code: Optional[str] = Query(None, description="外部订单号（我们系统的订单号）"),
        order_id: Optional[int] = Query(None, description="蜂鸟订单ID"),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    查询蜂鸟骑手信息接口

    需要管理员权限（miniapp:manage）

    参数（至少提供一个）：
    - partner_order_code: 外部订单号（我们系统的订单号）
    - order_id: 蜂鸟订单ID

    返回骑手信息，包括：
    - carrier_driver_id: 骑手ID
    - carrier_driver_name: 骑手姓名
    - carrier_driver_phone: 骑手电话
    - carrier_driver_longitude: 骑手位置经度（高德坐标）
    - carrier_driver_latitude: 骑手位置纬度（高德坐标）

    注意：沙箱环境返回的参数可能不全
    """
    logger.info(f"小程序管理员请求查询骑手信息，token: {token[:10] if token else 'None'}...")

    # 验证管理员权限
    if not verify_admin_permission(token, db):
        logger.warning("用户无管理员权限或token无效")
        return {
            "code": 403,
            "message": "无权限访问",
            "data": None
        }

    # 验证参数：至少提供一个
    if not partner_order_code and not order_id:
        logger.warning("未提供订单号或蜂鸟订单ID")
        return {
            "code": 400,
            "message": "请提供订单号(partner_order_code)或蜂鸟订单ID(order_id)中的至少一个",
            "data": None
        }

    try:
        # 构建查询参数
        query_params = {}
        if partner_order_code:
            query_params["partner_order_code"] = partner_order_code
        if order_id:
            query_params["order_id"] = order_id

        logger.info(f"查询参数: {query_params}")

        # 调用蜂鸟API
        fengniao_client = FengniaoClient()
        result = fengniao_client.get_knight_info(query_params)

        logger.info(f"蜂鸟API响应: {result}")

        # 检查蜂鸟API调用结果
        if result.get("code") != "200":
            logger.error(f"骑手信息查询失败: {result.get('msg', '未知错误')}")
            return {
                "code": 500,
                "message": f"查询失败: {result.get('msg', '未知错误')}",
                "data": None
            }

        # 解析业务数据
        business_data = result.get('business_data')
        if business_data:
            try:
                if isinstance(business_data, str):
                    business_data = json.loads(business_data)

                logger.info(f"骑手信息查询成功，骑手: {business_data.get('carrier_driver_name')}")

                return {
                    "code": 200,
                    "message": "success",
                    "data": business_data
                }
            except json.JSONDecodeError as e:
                logger.error(f"解析业务数据失败: {e}")
                return {
                    "code": 500,
                    "message": f"解析响应数据失败: {str(e)}",
                    "data": None
                }
        else:
            logger.warning("蜂鸟API未返回业务数据")
            return {
                "code": 500,
                "message": "未获取到骑手信息数据",
                "data": None
            }

    except Exception as e:
        logger.error(f"查询骑手信息失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return {
            "code": 500,
            "message": f"查询失败: {str(e)}",
            "data": None
        }


# API返回结果模拟数据参考示例
"""
外卖订单查询接口返回示例：

GET /api/v1/wechat_mini_app/admin/delivery/delivery_orders?start_date=2024-01-01&end_date=2024-01-31&page=1&page_size=10

{
    "code": 200,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 123,
                "order_no": "O20240115120000123456",
                "status": "completed",
                "payment_status": "paid",
                "payment_method": "wechat_pay",
                "total_amount": 68.50,
                "payable_amount": 68.50,
                "actual_amount_paid": 68.50,
                "discount_amount": 0.0,
                "created_at": "2024-01-15 12:00:00",
                "updated_at": "2024-01-15 12:30:00",
                "payment_time": "2024-01-15 12:05:00",
                "user_id": 456,
                "user_name": "张三",
                "user_phone": "13800138000",
                "user_nick_name": "美食爱好者",
                "delivery_address": "北京市朝阳区某某街道123号",
                "delivery_address_raw": {
                    "name": "张三",
                    "phone": "13800138000",
                    "province": "北京市",
                    "city": "北京市",
                    "district": "朝阳区",
                    "detail": "某某街道123号",
                    "latitude": 39.9042,
                    "longitude": 116.4074
                },
                "delivery_time": "2024-01-15 12:30:00",
                "delivery_time_raw": {
                    "type": "immediate",
                    "value": "立即配送"
                },
                "delivery_fee": 8.50,
                "fengniao_order_id": "FN20240115001",
                "fengniao_status": 4,
                "items": [
                    {
                        "id": 789,
                        "product_id": 101,
                        "product_name": "宫保鸡丁",
                        "quantity": 1,
                        "price": 28.00,
                        "subtotal": 28.00,
                        "final_price": 28.00,
                        "payable_amount": 28.00,
                        "pricing_remark": ""
                    },
                    {
                        "id": 790,
                        "product_id": 102,
                        "product_name": "蛋炒饭",
                        "quantity": 2,
                        "price": 16.00,
                        "subtotal": 32.00,
                        "final_price": 16.00,
                        "payable_amount": 32.00,
                        "pricing_remark": ""
                    }
                ]
            }
        ],
        "total": 25,
        "page": 1,
        "page_size": 10,
        "total_pages": 3
    }
}

错误响应示例：

{
    "code": 403,
    "message": "无权限访问",
    "data": null
}

{
    "code": 400,
    "message": "无效的开始日期格式，请使用 YYYY-MM-DD",
    "data": null
}

{
    "code": 500,
    "message": "查询失败: 数据库连接错误",
    "data": null
}

外卖订单打印接口返回示例：

POST /api/v1/wechat_mini_app/admin/delivery/print_delivery_orders?start_date=2024-01-01&end_date=2024-01-31&times=1

成功响应：
{
    "code": 200,
    "message": "成功打印 2/2 个订单",
    "data": {
        "printed_count": 2,
        "total_count": 2,
        "print_results": [
            {
                "order_id": 123,
                "order_no": "O20240115120000123456",
                "success": true,
                "message": "打印成功",
                "print_order_id": "816501678_20240115184316_1419533539"
            },
            {
                "order_id": 124,
                "order_no": "O20240115130000123457",
                "success": true,
                "message": "打印成功",
                "print_order_id": "816501678_20240115184320_1419533540"
            }
        ]
    }
}

部分成功响应：
{
    "code": 400,
    "message": "成功打印 1/2 个订单",
    "data": {
        "printed_count": 1,
        "total_count": 2,
        "print_results": [
            {
                "order_id": 123,
                "order_no": "O20240115120000123456",
                "success": true,
                "message": "打印成功",
                "print_order_id": "816501678_20240115184316_1419533539"
            },
            {
                "order_id": 124,
                "order_no": "O20240115130000123457",
                "success": false,
                "message": "打印内容超过5000字节限制"
            }
        ]
    }
}

错误响应：
{
    "code": 400,
    "message": "必须提供查询条件（order_id、order_ids 或 日期范围）",
    "data": {
        "printed_count": 0,
        "total_count": 0,
        "print_results": []
    }
}

{
    "code": 403,
    "message": "无权限访问",
    "data": null
}

{
    "code": 500,
    "message": "打印失败: 数据库连接错误",
    "data": null
}

蜂鸟订单详情查询接口返回示例：

GET /api/v1/wechat_mini_app/admin/delivery/order_detail?partner_order_code=O20240115120000123456

成功响应：
{
    "code": 200,
    "message": "success",
    "data": {
        "carrier_driver_name": "张三",
        "partner_order_code": "O20240115120000123456",
        "if_can_add_tip": 1,
        "claim_id": null,
        "remark": "请尽快送达",
        "abnormal_code": null,
        "order_actual_amount_cent": 850,
        "event_log_details": [
            {
                "order_status": 1,
                "occur_time": 1705294800000,
                "carrier_driver_name": "张三",
                "carrier_driver_phone": "138****5678"
            },
            {
                "order_status": 20,
                "occur_time": 1705295100000,
                "carrier_driver_name": "张三",
                "carrier_driver_phone": "138****5678"
            },
            {
                "order_status": 80,
                "occur_time": 1705295400000,
                "carrier_driver_name": "张三",
                "carrier_driver_phone": "138****5678"
            },
            {
                "order_status": 2,
                "occur_time": 1705295700000,
                "carrier_driver_name": "张三",
                "carrier_driver_phone": "138****5678"
            },
            {
                "order_status": 3,
                "occur_time": 1705296000000,
                "carrier_driver_name": "张三",
                "carrier_driver_phone": "138****5678"
            }
        ],
        "claim_reason_desc": null,
        "price_detail": {
            "weight_price_cent": 0,
            "pressure_surcharge_cent": 0,
            "time_period_surcharge_cent": 0,
            "distance_price_cent": 200,
            "river_crossing_surcharge_cent": 0,
            "category_surcharge_cent": 0,
            "temporary_surcharge_cent": 0,
            "order_price_surcharge_cent": 0,
            "start_price_cent": 650
        },
        "order_status": 3,
        "complaint_id": null,
        "order_total_amount_cent": 850,
        "complaint_reason_desc": null,
        "temperature": "骑手体温36.5℃",
        "delivery_fetch_photos": [],
        "cancel_code": null,
        "reverse_event_log_details": null,
        "tracking_id": 123456789,
        "carrier_driver_id": "rider_001",
        "overtime_compensation_cost_cent": 0,
        "claim_status": null,
        "abnormal_desc": null,
        "fetch_code": "1234",
        "serial_number": "001",
        "order_distance": 2500.0,
        "order_tip_amount_cent": 0,
        "complaint_status": null,
        "write_off_code": "5678",
        "carrier_driver_phone": "138****5678",
        "order_id": 987654321,
        "estimate_arrive_time": 1705296000000,
        "claim_status_desc": null,
        "complaint_response_desc": null,
        "weather_info": {
            "weatherRate": 0,
            "weatherDesc": "晴天"
        }
    }
}

错误响应：
{
    "code": 400,
    "message": "请提供订单号(partner_order_code)或蜂鸟订单ID(order_id)中的至少一个",
    "data": null
}

{
    "code": 403,
    "message": "无权限访问",
    "data": null
}

{
    "code": 500,
    "message": "查询失败: 订单不存在",
    "data": null
}

蜂鸟骑手信息查询接口返回示例：

GET /api/v1/wechat_mini_app/admin/delivery/rider_info?partner_order_code=O20240115120000123456

成功响应：
{
    "code": 200,
    "message": "success",
    "data": {
        "carrier_driver_id": "rider_001",
        "carrier_driver_name": "张三",
        "carrier_driver_latitude": "39.9042",
        "carrier_driver_phone": "138****5678",
        "carrier_driver_longitude": "116.4074"
    }
}

错误响应：
{
    "code": 400,
    "message": "请提供订单号(partner_order_code)或蜂鸟订单ID(order_id)中的至少一个",
    "data": null
}

{
    "code": 403,
    "message": "无权限访问",
    "data": null
}

{
    "code": 500,
    "message": "查询失败: 订单不存在或骑手信息不可用",
    "data": null
}

注意事项：
1. 订单状态说明：
   - 0: 订单生成
   - 1: 运单生成成功
   - 20: 骑手接单
   - 80: 骑手到店
   - 2: 配送中
   - 3: 已完成
   - 4: 已取消
   - 5: 配送异常

2. 金额单位：所有金额字段（如 order_actual_amount_cent）单位为分，需要除以100转换为元

3. 时间单位：所有时间字段（如 estimate_arrive_time, occur_time）单位为毫秒级时间戳

4. 骑手位置：carrier_driver_longitude 和 carrier_driver_latitude 返回的是高德坐标系的经纬度

5. 沙箱环境：在沙箱环境中，查询的参数可能不全，实际生产环境会返回完整信息
"""