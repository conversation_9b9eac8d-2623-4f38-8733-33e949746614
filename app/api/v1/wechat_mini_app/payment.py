# -*- coding: utf-8 -*-
# 支付模块


from fastapi import APIRouter, Depends, HTTPException, Header, File, UploadFile, Request
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import re

from app.dao.account import account_transaction_dao, account_dao
from app.models.enum import Status
from app.service.wechat_miniapp.wx_reservation import reservation_service
from app.core.config import settings
from app.service.wechat_miniapp.wx_service import WechatService
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.service.user import UserService
from app.service.wechat_miniapp.wx_order import WXOrderService
from app.models.user import PersonalUser
from app.dao.rule import rule_dao
from app.models.order import Order
from app.core.deps import get_current_user, get_db
from app.service.wechat_miniapp.wx_account import AccountService
import logging
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from app.schemas.user import PersonalUserCreateWX
import random
import string
import os
from pathlib import Path

from app.utils.common import get_phone_number, product_wx_token, allowed_file, secure_filename
from app.models.account import AccountType, TransactionType
from app.schemas.order import OrderUpdate
from app.models.order import PaymentStatus, OrderType, PaymentMethod
from app.dao.order import order_dao, order_item_dao
from app.service.wechat_miniapp.wx_service import wechat_service
from app.service.payment import payment_service
from app.dao.reservation import reservation_request_dao
from app.models.reservation import ReservationStatus
from app.models.order import OrderStatus, OrderItem
from app.dao.wx_payment import wx_payment_dao, wx_payment_service
from app.models.order import WxPaymentRecord, WxRefundRecord, WxRefundStatus, WxPaymentStatus
from app.schemas.account import AccountTransactionCreate
from app.utils.common import get_current_time
from app.dao.user import personal_user_dao
from app.dao.product import product_dao
from app.service.order import order_service
from app.utils.logger import logger
from app.dao.rule import rule_item_dao
from app.service.revervation import reservation_service as revervation_service
from app.dao.reservation import biz_reservation_request_dao
from app.schemas.reservation import BizReservationRequestCreate
from app.dao.menu import menu_dao
from app.models.reservation import ReservationType
from app.models.rule import DiningReservationRule, RuleItem, RuleScope, RuleType, RuleOrderType
from app.models.product import MealType
from app.models.reservation import BizReservationRequest
from app.dao.user import enterprise_user_relation_dao, enterprise_dao
from app.dao.admin import admin_dao
from app.service.pricing import pricing_service
from app.schemas.order import OrderItemBase
from app.events.deps import EventBusDep
from app.events.models import OrderEvent, OrderEventAction
from app.core.scheduler import send_order_change_reminders_task
from app.api.v1.wechat_mini_app.biz_dinner import update_order_items_payment_status
from app.service.fengniao import FengniaoClient

router = APIRouter()


@router.post("/wxpay/notify")
async def wxpay_notify(request: Request, event_bus: EventBusDep, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    处理微信支付回调
    """
    try:
        logger.info("开始处理微信支付回调")

        # 获取原始请求数据
        body = await request.body()
        headers = request.headers
        logger.info(f"收到回调请求 - Headers: {headers}")
        logger.info(f"收到回调请求 - Body: {body}")

        # 验证签名和解密数据
        logger.info("开始验证签名和解密数据")
        data = wechat_service.wechatpay.callback(headers, body)
        logger.info(f"解密后的回调数据: {data}")

        if not data:
            logger.error("回调数据验证失败")
            return {"code": "FAIL", "message": "回调数据验证失败"}

        # 解析回调中的事件类型
        event_type = data.get("event_type", "")
        logger.info(f"回调事件类型: {event_type}")

        # 根据事件类型分别处理支付成功和退款成功
        if event_type == "TRANSACTION.SUCCESS":
            # 支付成功事件处理
            resource = data.get("resource", {})
            out_trade_no = resource.get("out_trade_no")

            # 检查订单是否已经处理过
            order = order_dao.get_by_order_no(db, out_trade_no)
            if order and order.payment_status == PaymentStatus.PAID:
                logger.info(f"订单 {out_trade_no} 已处理过支付成功回调，忽略本次请求")
                return {"code": "SUCCESS", "message": "订单已处理"}

            return await handle_payment_success(data, db, event_bus)
        elif event_type == "REFUND.SUCCESS":
            # 退款成功事件处理
            resource = data.get("resource", {})
            refund_id = resource.get("refund_id")

            # 检查退款是否已经处理过
            existing_refund = db.query(WxRefundRecord).filter(
                WxRefundRecord.refund_id == refund_id
            ).first()

            if existing_refund:
                logger.info(f"退款记录已存在，退款ID: {refund_id}，跳过处理")
                return {"code": "SUCCESS", "message": "退款已处理"}

            return await handle_refund_success(data, db)
        else:
            logger.warning(f"未知的事件类型: {event_type}")
            return {"code": "SUCCESS", "message": "事件类型不处理"}

    except Exception as e:
        logger.error(f"处理支付回调异常: {str(e)}", exc_info=True)
        return {"code": "FAIL", "message": str(e)}


async def handle_payment_success(data: Dict[str, Any], db: Session, event_bus: EventBusDep) -> Dict[str, Any]:
    """处理支付成功事件"""
    try:
        # 解析回调数据
        resource = data.get("resource", {})  # 获取 resource 字段
        transaction_id = resource.get("transaction_id")  # 微信支付订单号
        out_trade_no = resource.get("out_trade_no")  # 商户订单号
        trade_state = resource.get("trade_state")  # 交易状态
        trade_state_desc = resource.get("trade_state_desc")  # 交易状态描述
        amount = resource.get("amount", {}).get("total", 0)  # 订单金额
        payer_total = resource.get("amount", {}).get("payer_total", 0)  # 用户实付金额
        bank_type = resource.get("bank_type")  # 付款银行类型
        payer_openid = resource.get("payer", {}).get("openid")  # 支付者openid
        success_time_str = resource.get("success_time")  # 支付成功时间

        # 转换支付成功时间为 datetime 对象
        success_time = None
        if success_time_str:
            try:
                # 处理时间格式，例如：2025-04-27T14:07:01+08:00
                success_time = datetime.fromisoformat(success_time_str)
            except Exception as e:
                logger.warning(f"支付成功时间格式转换失败: {e}")

        logger.info(f"""解析支付成功回调数据:
            - 微信支付订单号: {transaction_id}
            - 商户订单号: {out_trade_no}
            - 交易状态: {trade_state}
            - 交易状态描述: {trade_state_desc}
            - 订单金额(分): {amount}
            - 用户实付金额(分): {payer_total}
        """)

        # 检查是否是分次支付的个人部分
        is_split_payment_personal = "_personal" in out_trade_no
        is_modified_payment = "_" in out_trade_no and not is_split_payment_personal
        original_order_id = None

        if is_split_payment_personal:
            # 处理分次支付的个人部分
            logger.info(f"检测到分次支付个人部分的回调: {out_trade_no}")
            original_order_no = out_trade_no.replace("_personal", "")
            order = order_dao.get_by_order_no(db, original_order_no)

            if not order:
                logger.error(f"分次支付原始订单不存在，订单号: {original_order_no}")
                return {"code": "FAIL", "message": "原始订单不存在"}

            if order.payment_status != PaymentStatus.PARTIAL_PAID:
                logger.error(f"订单不在部分支付状态，订单号: {original_order_no}, 状态: {order.payment_status}")
                return {"code": "FAIL", "message": "订单状态不正确"}

            # 立即获取并保存所有需要的信息
            order_id = order.id
            user_id = order.user_id
            requires_personal_payment = order.requires_personal_payment

            logger.info(
                f"开始处理分次支付个人部分 - 订单ID: {order_id}, 用户ID: {user_id}, 应付金额: {requires_personal_payment}, 实付金额: {amount / 100}")

            # 验证支付金额是否正确
            expected_amount = requires_personal_payment * 100  # 转换为分
            if amount != expected_amount:
                logger.error(f"分次支付金额不匹配 - 期望: {expected_amount}分, 实际: {amount}分")
                return {"code": "FAIL", "message": "支付金额不匹配"}

            # 1. 查询是否已存在支付记录
            payment_record = wx_payment_dao.get_payment_by_order_no(db, out_trade_no)

            # 2. 如果支付记录不存在，则创建新记录
            if not payment_record:
                logger.info(f"分次支付记录不存在，创建新记录，订单号: {out_trade_no}")
                # 创建支付记录
                payment_record = wx_payment_dao.create_payment_record(
                    db,
                    order_id=order_id,
                    order_no=out_trade_no,
                    openid=payer_openid,
                    total_amount=amount / 100,  # 转换为元
                    description=f"分次支付个人部分-{out_trade_no}"
                )
                if not payment_record:
                    logger.error(f"创建分次支付记录失败，订单号: {out_trade_no}")

            # 3. 更新支付记录状态
            updated_payment = wx_payment_dao.update_payment_status(
                db,
                order_no=out_trade_no,
                transaction_id=transaction_id,
                trade_state=trade_state,
                trade_state_desc=trade_state_desc,
                payer_total=payer_total / 100,  # 转换为元
                notify_data=resource,
                success_time=success_time,
                bank_type=bank_type
            )

            if updated_payment:
                logger.info(f"分次支付记录状态更新成功，订单号: {out_trade_no}")
            else:
                logger.error(f"分次支付记录状态更新失败，订单号: {out_trade_no}")

            # 4. 处理分次支付的微信支付完成
            try:
                payment_info = {
                    "payment_method": PaymentMethod.WECHAT_PAY,
                    "amount": requires_personal_payment
                }

                # 完成分次支付
                completed_order = payment_service.complete_split_payment_wechat(
                    db, order_id, payment_info
                )

                if completed_order:
                    logger.info(f"分次支付微信支付完成 - 订单: {original_order_no}")

                    # 更新产品库存
                    from app.service.order import order_service
                    order_service.update_product_stock(db, order_id)
                    logger.info(f"分次支付完成，产品库存更新成功，订单ID: {order_id}")

                    # TODO：分次支付成功，虚拟产品交付（混合支付个人部分）
                    # 如果是虚拟产品，执行交付

                    return {"code": "SUCCESS", "message": "分次支付处理成功"}
                else:
                    logger.error(f"分次支付微信支付处理失败 - 订单: {original_order_no}")
                    return {"code": "FAIL", "message": "分次支付处理失败"}

            except Exception as e:
                logger.error(f"分次支付微信支付处理异常: {str(e)}", exc_info=True)
                return {"code": "FAIL", "message": f"分次支付处理异常: {str(e)}"}

        elif is_modified_payment:
            # 处理修改后的订单支付（原有逻辑）
            logger.info(f"检测到修改后订单的支付回调: {out_trade_no}")
            # 从支付记录中获取关联的原始订单
            payment_record = wx_payment_dao.get_payment_by_order_no(db, out_trade_no)
            if payment_record:
                original_order_id = payment_record.order_id
                order = order_dao.get(db, original_order_id)
            else:
                # 如果找不到支付记录，尝试解析订单号
                try:
                    original_order_no = out_trade_no.split("_")[0]
                    logger.info(f"从修改后订单号解析出原始订单号: {original_order_no}")
                    order = order_dao.get_by_order_no(db, original_order_no)
                except Exception as e:
                    logger.error(f"解析修改后订单号失败: {str(e)}")
                    order = None
        else:
            # 正常查询订单
            order = order_dao.get_by_order_no(db, out_trade_no)

        if not order:
            logger.error(f"订单不存在，商户订单号: {out_trade_no}")
            return {"code": "FAIL", "message": "订单不存在"}

        # 立即获取并保存所有需要的信息
        order_id = order.id
        order_type = order.type
        user_id = order.user_id

        logger.info(f"查询到订单信息: ID={order_id}, 类型={order_type}")
        logger.info(f"微信支付回调，所获得的订单对象的所有值:")
        for key, value in order.__dict__.items():
            if not key.startswith('_sa_'):  # 过滤掉 SQLAlchemy 内部属性
                logger.info(f"  {key}: {value}")

        # 如果是分次支付的个人部分，已在上面处理完毕，直接返回
        if is_split_payment_personal:
            return {"code": "SUCCESS", "message": "分次支付个人部分已处理"}

        # 使用已保存的变量，不再访问order对象的属性
        logger.info(f"开始更新订单支付状态以及创建支付记录，订单ID: {order_id}")

        # 1. 查询是否已存在支付记录
        payment_record = wx_payment_dao.get_payment_by_order_no(db, out_trade_no)

        # 2. 如果支付记录不存在，则创建新记录
        if not payment_record:
            logger.info(f"支付记录不存在，创建新记录，订单号: {out_trade_no}")
            # 创建支付记录
            payment_record = wx_payment_dao.create_payment_record(
                db,
                order_id=order_id,
                order_no=out_trade_no,
                openid=payer_openid,
                total_amount=amount / 100,  # 转换为元
                description=f"订单支付-{out_trade_no}" + ("(修改后差额)" if is_modified_payment else "")
            )
            if not payment_record:
                logger.error(f"创建支付记录失败，订单号: {out_trade_no}")
            if is_modified_payment:
                if original_order_id:
                    send_order_change_reminders_task(original_order_id, "已支付")
                else:
                    send_order_change_reminders_task(order_id, "已支付")

        # 3. 更新支付记录状态
        updated_payment = wx_payment_dao.update_payment_status(
            db,
            order_no=out_trade_no,
            transaction_id=transaction_id,
            trade_state=trade_state,
            trade_state_desc=trade_state_desc,
            payer_total=payer_total / 100,  # 转换为元
            notify_data=resource,
            success_time=success_time,
            bank_type=bank_type
        )

        if updated_payment:
            logger.info(f"支付记录状态更新成功，订单号: {out_trade_no}")
        else:
            logger.error(f"支付记录状态更新失败，订单号: {out_trade_no}")

        # 更新订单状态
        logger.info(f"开始更新订单支付状态，订单ID: {order_id}")

        paid_order = None

        if is_modified_payment:
            # 处理修改后的订单支付（原有逻辑保持不变）
            logger.info(f"处理修改后订单的支付，订单ID: {order_id}")

            # 获取订单项计算当前总金额
            order_items = order_item_dao.get_by_order(db, order_id)
            current_items_amount = 0
            for item in order_items:
                if item.status != OrderStatus.CANCELLED and str(item.product_id) != '100' and str(
                        item.product_id) != '200':
                    current_items_amount += item.price * item.quantity

            # 更新订单状态
            order = order_dao.get(db, order_id)
            order.is_modified = True
            order.last_modified_at = datetime.now()
            order.current_status = "商务餐订单支付，微信支付进行补差额：" + str(amount / 100)

            # 更新订单总金额
            order.total_amount = current_items_amount
            order.actual_amount_paid = order.total_amount

            db.commit()
            logger.info(f"修改后订单支付状态更新成功，订单ID: {order_id}")

            # 同步更新订单项状态
            logger.info(f"开始同步更新订单项状态，订单ID: {order_id}")
            update_order_items_payment_status(db, order_id)
            # TODO: 订单支付成功，虚拟产品交付（修改后订单）
            db.commit()

        elif order_type == OrderType.RESERVATION:
            # 预订订单（原有逻辑保持不变）
            payment_info = {
                "payment_method": PaymentMethod.WECHAT_PAY
            }
            paid_order = payment_service.pay_order(db, order_id, payment_info)
            logger.info(f"预订订单支付成功")

            # 更新产品库存 - 使用支付后返回的订单ID
            from app.service.order import order_service
            order_service.update_product_stock(db, paid_order.id)
            logger.info(f"产品库存更新成功，订单ID: {order_id}")

        elif order_type == OrderType.DELIVERY:
            # 外卖订单处理
            logger.info(f"外卖订单：微信支付成功，开始进行账户流水处理 - 订单号: {out_trade_no}, 用户ID: {user_id}")
            payment_info = {
                "payment_method": PaymentMethod.WECHAT_PAY
            }
            paid_order = payment_service.pay_order(db, order_id, payment_info)
            logger.info(f"外卖订单：微信支付过程结束 - 订单号: {out_trade_no}, 用户ID: {user_id}")

            # 更新产品库存 - 使用支付后返回的订单ID
            from app.service.order import order_service
            order_service.update_product_stock(db, paid_order.id)
            logger.info(f"产品库存更新成功，订单ID: {order_id}")

            # 调用蜂鸟下单接口
            logger.info(f"外卖订单微信支付成功，开始调用蜂鸟下单接口，订单ID: {order_id}")
            try:
                # 获取外卖订单详细信息
                delivery_order = order_dao.get(db, order_id)
                if hasattr(delivery_order, 'delivery_fee') and hasattr(delivery_order, 'service_goods_id'):
                    # 调用蜂鸟下单接口
                    fengniao_result = await _create_fengniao_order_for_wechat_payment(db, delivery_order)
                    if not fengniao_result["success"]:
                        logger.error(f"蜂鸟下单失败: {fengniao_result['message']}")
                        # 如果是配送费变动，发起退款
                        if "配送费发生变动" in fengniao_result["message"]:
                            logger.info(f"配送费发生变动，开始发起退款，订单ID: {order_id}")
                            await _initiate_refund_for_delivery_fee_change(db, delivery_order, amount / 100)
                        # 注意：这里不抛出异常，因为微信支付已经成功，只是蜂鸟下单失败
                    else:
                        logger.info(f"蜂鸟下单成功，订单ID: {order_id}, 蜂鸟订单号: {fengniao_result.get('fengniao_order_id')}")
                else:
                    logger.warning(f"外卖订单缺少必要的蜂鸟参数，订单ID: {order_id}")
            except Exception as e:
                logger.error(f"调用蜂鸟下单接口异常: {str(e)}", exc_info=True)
                # 注意：这里不抛出异常，因为微信支付已经成功

        elif order_type == OrderType.DIRECT_SALE:
            logger.info(f"直销订单：微信支付成功，开始进行账户流水处理 - 订单号: {out_trade_no}, 用户ID: {user_id}")
            payment_info = {
                "payment_method": PaymentMethod.WECHAT_PAY
            }
            paid_order = payment_service.pay_order(db, order_id, payment_info)
            logger.info(f"直销订单：微信支付过程结束 - 订单号: {out_trade_no}, 用户ID: {user_id}")

            # 更新产品库存 - 使用支付后返回的订单ID
            from app.service.order import order_service
            order_service.update_product_stock(db, paid_order.id)
            logger.info(f"产品库存更新成功，订单ID: {order_id}")

        elif order_type == OrderType.RECHARGE:
            # 账户充值（原有逻辑保持不变）
            logger.info(f"订单类型: {order_type}")
            logger.info("充值订单更新支付状态")
            order_dao.update_payment_status(
                db,
                order_id,
                payment_status=PaymentStatus.PAID,
                status=OrderStatus.PAID
            )
            logger.info("充值订单更新支付状态成功")
            logger.info(f"充值订单：微信支付成功，开始进行账户流水处理 - 订单号: {out_trade_no}, 用户ID: {user_id}, 金额: {amount / 100}")
            recharge_result = AccountService.process_recharge(
                db,
                user_id=user_id,
                amount=amount / 100,
                order_id=order_id
            )

            if not recharge_result:
                logger.error(f"充值处理失败 - 订单号: {out_trade_no}, 用户ID: {user_id}")
                return {"code": "FAIL", "message": "充值处理失败"}

            paid_order = order_dao.get(db, order_id)
            logger.info(f"充值订单：微信支付过程结束 - 订单号: {out_trade_no}, 用户ID: {user_id}, 金额: {amount / 100}")
        else:
            logger.info(f"一般订单：微信支付成功，开始进行账户流水处理 - 订单号: {out_trade_no}, 用户ID: {user_id}")
            payment_info = {
                "payment_method": PaymentMethod.WECHAT_PAY
            }
            w = payment_service.pay_order(db, order_id, payment_info)
            logger.info(f"一般订单：微信支付过程结束 - 订单号: {out_trade_no}, 用户ID: {user_id}")

            # 更新产品库存 - 使用支付后返回的订单ID
            from app.service.order import order_service
            order_service.update_product_stock(db, paid_order.id)
            logger.info(f"产品库存更新成功，订单ID: {order_id}")

        try:
            if paid_order:
                order_event = OrderEvent(
                    action=OrderEventAction.PAID,
                    order_id=paid_order.id,
                    order_no=paid_order.order_no,
                    user_id=paid_order.user_id,
                    amount=paid_order.payable_amount,  # 转换为元
                    status=str(paid_order.status),
                    payment_status=str(paid_order.payment_status),
                    source="miniapp_order_payment_by_wechat",
                    type=order.type.value
                )
                await event_bus.publish(order_event)
                logger.info(f"订单支付成功事件(微信支付)已发布: {order_event.id}")
            else:
                logger.info(f"订单支付成功事件(微信支付)未发布: 未包含有 paid_order")
        except Exception as e:
            logger.error(f"发布订单支付成功事件(微信支付)失败: {e}")

        logger.info("微信支付成功回调处理完成")
        return {"code": "SUCCESS", "message": "成功"}
    except Exception as e:
        logger.error(f"处理支付成功回调异常: {str(e)}", exc_info=True)
        return {"code": "FAIL", "message": str(e)}


async def handle_refund_success(data: Dict[str, Any], db: Session) -> Dict[str, Any]:
    """处理退款成功事件"""
    try:
        # 解析退款回调数据
        resource = data.get("resource", {})
        refund_id = resource.get("refund_id")  # 微信退款单号
        out_refund_no = resource.get("out_refund_no")  # 商户退款单号

        # 检查是否已处理过该退款ID
        existing_refund = db.query(WxRefundRecord).filter(
            WxRefundRecord.refund_id == refund_id
        ).first()

        if existing_refund:
            logger.info(f"退款记录已存在，退款ID: {refund_id}，跳过处理")
            return {"code": "SUCCESS", "message": "退款已处理"}

        transaction_id = resource.get("transaction_id")  # 微信支付订单号
        out_trade_no = resource.get("out_trade_no")  # 商户订单号

        # 获取金额信息
        amount = resource.get("amount", {})
        total_amount = amount.get("total", 0)  # 订单总金额(分)
        refund_amount = amount.get("refund", 0)  # 退款金额(分)

        # 获取退款成功时间
        success_time_str = resource.get("success_time")
        refund_status = resource.get("refund_status")
        refund_reason = resource.get("reason", "")

        # 转换退款成功时间为 datetime 对象
        success_time = None
        if success_time_str:
            try:
                success_time = datetime.fromisoformat(success_time_str)
            except Exception as e:
                logger.warning(f"退款成功时间格式转换失败: {e}")

        logger.info(f"""解析退款成功回调数据:
            - 微信退款单号: {refund_id}
            - 商户退款单号: {out_refund_no}
            - 微信支付订单号: {transaction_id}
            - 商户订单号: {out_trade_no}
            - 订单总金额(分): {total_amount}
            - 退款金额(分): {refund_amount}
            - 退款状态: {refund_status}
        """)

        # 获取支付记录
        payment_record = wx_payment_dao.get_payment_by_transaction_id(db, transaction_id)
        if not payment_record:
            logger.error(f"找不到关联的支付记录，微信支付单号: {transaction_id}")
            return {"code": "FAIL", "message": "找不到关联的支付记录"}

        payment_record_id = payment_record.id
        # payment_record_status = payment_record.status
        payment_record_order_id = payment_record.order_id

        # 创建退款记录
        try:
            refund_record = WxRefundRecord(
                payment_id=payment_record_id,
                refund_id=refund_id,
                out_refund_no=out_refund_no,
                transaction_id=transaction_id,
                total_amount=total_amount / 100,  # 转换为元
                refund_amount=refund_amount / 100,  # 转换为元
                status=WxRefundStatus.SUCCESS,
                reason=refund_reason,
                refund_time=success_time,
                notify_data=resource,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )

            db.add(refund_record)
            db.commit()

            # 不使用 db.refresh，直接从数据库重新查询记录
            # refund_record = db.query(WxRefundRecord).filter(WxRefundRecord.refund_id == refund_id).first()

            logger.info(f"退款记录创建成功，退款单号: {refund_id}")

            # 更新支付记录状态为退款
            payment_record.status = WxPaymentStatus.REFUND
            db.commit()

            # 更新订单状态
            try:
                order_id = payment_record_order_id
                # 获取订单总金额
                order = order_dao.get(db, order_id)
                if not order:
                    raise ValueError("订单不存在")

                order_user_id = order.user_id
                order_payment_status = order.payment_status

                # 判断是部分退款还是全部退款
                logger.info(f"订单总金额: {total_amount}, 退款金额: {refund_amount}")

                # 判断是否为商务餐订单
                is_business_dining = False
                for reservation_request in order.reservation_requests:
                    if hasattr(reservation_request,
                               'type') and reservation_request.type == ReservationType.BIZ_DINING_RESERVATION:
                        is_business_dining = True
                        break

                logger.info(f"订单类型: {'商务餐' if is_business_dining else '自助餐'}")

                try:
                    # 1. 获取微信账户
                    accounts = account_dao.get_by_user_id(db, order_user_id)
                    if not accounts:
                        raise ValueError("找不到关联的账户")

                    # 获取微信账户
                    wechat_accounts = [
                        account for account in accounts
                        if account.type == AccountType.WECHAT and account.status == Status.ACTIVE
                    ]

                    if not wechat_accounts:
                        raise ValueError("用户微信账户不存在")

                    wechat_account = wechat_accounts[0]

                    # 2. 记录详细的日志
                    logger.info(
                        f"开始处理退款 - 订单ID: {order_id}, 用户ID: {order_user_id}, 退款金额: {refund_amount / 100}")
                    logger.info(f"微信账户ID: {wechat_account.id}, 当前余额: {wechat_account.balance}")

                    try:
                        if is_business_dining:
                            # 商务餐订单：微信退款处理（先加后减，保持微信账户余额为0）
                            logger.info(f"商务餐订单微信退款处理 - 订单ID: {order_id}")

                            # 1. 先将退款金额加到微信账户
                            old_wechat_balance = wechat_account.balance
                            wechat_account.balance = wechat_account.balance + refund_amount / 100
                            db.add(wechat_account)
                            logger.info(
                                f"微信账户收到商务餐退款 - 账户ID: {wechat_account.id}, 原余额: {old_wechat_balance}, 新余额: {wechat_account.balance}")

                            # 创建微信账户收到退款的交易记录
                            wechat_refund_deposit = AccountTransactionCreate(
                                account_id=wechat_account.id,
                                amount=refund_amount / 100,
                                transaction_type=TransactionType.DEPOSIT,
                                description=f"商务餐微信退款入账 +{refund_amount / 100}元",
                                order_id=order_id,
                                transaction_time=get_current_time()
                            )
                            account_transaction_dao.create(db, wechat_refund_deposit)

                            # 2. 立即从微信账户扣除退款金额（保持微信账户余额为0）
                            old_wechat_balance = wechat_account.balance
                            wechat_account.balance = wechat_account.balance - refund_amount / 100
                            db.add(wechat_account)
                            logger.info(
                                f"微信账户商务餐退款支出 - 账户ID: {wechat_account.id}, 原余额: {old_wechat_balance}, 新余额: {wechat_account.balance}")

                            # 创建微信账户退款支出记录
                            wechat_refund_payment = AccountTransactionCreate(
                                account_id=wechat_account.id,
                                amount=-refund_amount / 100,
                                transaction_type=TransactionType.REFUND,
                                description=f"商务餐订单退款：{order_id}",
                                order_id=order_id,
                                transaction_time=get_current_time()
                            )
                            account_transaction_dao.create(db, wechat_refund_payment)

                            # 更新订单状态为已退款
                            order = order_dao.get(db, order_id)
                            order.payment_status = PaymentStatus.REFUNDED
                            order.status = OrderStatus.REFUNDED
                            order.payment_time = get_current_time()
                            db.add(order)

                        else:
                            # 自助餐订单：原有的处理逻辑（先加后减）
                            logger.info(f"自助餐订单微信退款处理 - 订单ID: {order_id}")

                            # 3. 先将退款金额加到微信账户
                            old_wechat_balance = wechat_account.balance
                            wechat_account.balance = wechat_account.balance + refund_amount / 100
                            db.add(wechat_account)
                            logger.info(
                                f"微信账户收到退款 - 账户ID: {wechat_account.id}, 原余额: {old_wechat_balance}, 新余额: {wechat_account.balance}")

                            # 创建微信账户收到退款的交易记录
                            wechat_refund_deposit = AccountTransactionCreate(
                                account_id=wechat_account.id,
                                amount=refund_amount / 100,
                                transaction_type=TransactionType.DEPOSIT,
                                description=f"微信退款入账 +{refund_amount / 100}元",
                                order_id=order_id,
                                transaction_time=get_current_time()
                            )
                            account_transaction_dao.create(db, wechat_refund_deposit)

                            # 4. 从微信账户扣除退款金额
                            old_wechat_balance = wechat_account.balance
                            wechat_account.balance = wechat_account.balance - refund_amount / 100
                            db.add(wechat_account)
                            logger.info(
                                f"微信账户退款支出 - 账户ID: {wechat_account.id}, 原余额: {old_wechat_balance}, 新余额: {wechat_account.balance}")

                            # 创建微信账户退款支出记录
                            wechat_refund_payment = AccountTransactionCreate(
                                account_id=wechat_account.id,
                                amount=-refund_amount / 100,
                                transaction_type=TransactionType.REFUND,
                                description=f"订单退款：{order_id}",
                                order_id=order_id,
                                transaction_time=get_current_time()
                            )
                            account_transaction_dao.create(db, wechat_refund_payment)

                            # 5. 更新订单状态
                            if total_amount == refund_amount:
                                # 全部退款
                                status = OrderStatus.REFUNDED
                                payment_status = PaymentStatus.REFUNDED
                            else:
                                # 部分退款
                                status = OrderStatus.REFUNDED_PARTIAL
                                payment_status = order_payment_status

                            order = order_dao.get(db, order_id)
                            order.payment_status = payment_status
                            order.status = status
                            order.payment_time = get_current_time()
                            db.add(order)

                        # 提交事务
                        db.commit()
                        logger.info(
                            f"退款处理成功 - 订单ID: {order_id}, 订单类型: {'商务餐' if is_business_dining else '自助餐'}")

                    except Exception as e:
                        db.rollback()
                        logger.error(f"退款处理失败: {str(e)}")
                        raise

                except Exception as e:
                    logger.error(f"退款处理异常: {str(e)}")
                    raise

            except Exception as e:
                db.rollback()
                logger.error(f"更新订单状态失败: {str(e)}")
                return {"code": "FAIL", "message": f"更新订单状态失败: {str(e)}"}

            return {"code": "SUCCESS", "message": "退款记录处理成功"}

        except Exception as e:
            db.rollback()
            logger.error(f"创建退款记录失败: {str(e)}")
            return {"code": "FAIL", "message": f"创建退款记录失败: {str(e)}"}

    except Exception as e:
        logger.error(f"处理退款成功回调异常: {str(e)}", exc_info=True)
        return {"code": "FAIL", "message": str(e)}


async def _create_fengniao_order_for_wechat_payment(db: Session, delivery_order) -> Dict[str, Any]:
    """
    微信支付成功后调用蜂鸟下单接口创建配送订单

    Args:
        db: 数据库会话
        delivery_order: 外卖订单对象

    Returns:
        Dict: 包含成功状态和结果信息的字典
    """
    try:
        logger.info(f"微信支付成功后开始调用蜂鸟下单接口，订单ID: {delivery_order.id}")

        # 检查必要的蜂鸟参数
        if not all([
            delivery_order.delivery_fee,
            delivery_order.service_goods_id,
            delivery_order.base_goods_id,
            delivery_order.t_index_id
        ]):
            logger.error(f"外卖订单缺少必要的蜂鸟参数，订单ID: {delivery_order.id}")
            return {
                "success": False,
                "message": "外卖订单缺少必要的蜂鸟参数"
            }

        # 构建蜂鸟下单请求数据
        order_data = {
            "partner_order_code": delivery_order.order_no,
            "receiver_primary_phone": delivery_order.delivery_address_raw.get("phone") if delivery_order.delivery_address_raw else "",
            "actual_delivery_amount_cent": int(delivery_order.delivery_fee * 100),  # 转换为分
            "goods_count": len(delivery_order.items),
            "service_goods_id": delivery_order.service_goods_id,
            "receiver_latitude": delivery_order.delivery_address_raw.get("latitude") if delivery_order.delivery_address_raw else 0,
            "goods_total_amount_cent": int(delivery_order.total_amount * 100),  # 转换为分
            "receiver_name": delivery_order.delivery_address_raw.get("name") if delivery_order.delivery_address_raw else "",
            "pre_create_order_t_index_id": delivery_order.t_index_id,
            "order_type": 1,  # 即时单
            "goods_weight": 1.0,  # 默认重量
            "goods_actual_amount_cent": int(delivery_order.total_amount * 100),  # 转换为分
            "receiver_longitude": delivery_order.delivery_address_raw.get("longitude") if delivery_order.delivery_address_raw else 0,
            "base_goods_id": delivery_order.base_goods_id,
            "receiver_address": delivery_order.delivery_address,
            "position_source": 3,  # 高德地图
            "goods_item_list": []
        }

        # 根据配置的配送模式添加相应参数
        _add_delivery_mode_params_for_payment(order_data)

        # 构建商品列表
        for item in delivery_order.items:
            goods_item = {
                "item_actual_amount_cent": int(item.payable_amount * 100),  # 转换为分
                "item_amount_cent": int(item.price * item.quantity * 100),  # 转换为分
                "item_name": item.product.name if item.product else f"商品{item.product_id}",
                "item_id": str(item.product_id),
                "item_quantity": item.quantity
            }
            order_data["goods_item_list"].append(goods_item)

        # 调用蜂鸟API
        fengniao_client = FengniaoClient()
        result = fengniao_client.create_order(order_data)

        logger.info(f"蜂鸟下单接口返回结果: {result}")

        # 检查返回结果
        if result.get("code") == "200":
            # 解析蜂鸟订单ID
            business_data = result.get("business_data")
            if business_data:
                import json
                if isinstance(business_data, str):
                    business_data = json.loads(business_data)
                fengniao_order_id = business_data.get("order_id")

                if fengniao_order_id:
                    # 更新订单的蜂鸟订单号
                    delivery_order.fengniao_order_id = str(fengniao_order_id)
                    delivery_order.fengniao_status = 1  # 运单生成成功
                    db.commit()

                    logger.info(f"蜂鸟下单成功，订单ID: {delivery_order.id}, 蜂鸟订单号: {fengniao_order_id}")
                    return {
                        "success": True,
                        "message": "蜂鸟下单成功",
                        "fengniao_order_id": fengniao_order_id
                    }

        # 检查是否是价格不一致错误
        error_code = result.get("code", "")
        error_msg = result.get("msg", "")

        if error_code == "B0109":
            logger.error(f"蜂鸟下单失败 - 配送费发生变动，订单ID: {delivery_order.id}")
            return {
                "success": False,
                "message": "配送费发生变动，需要退款"
            }
        else:
            logger.error(f"蜂鸟下单失败，订单ID: {delivery_order.id}, 错误码: {error_code}, 错误信息: {error_msg}")
            return {
                "success": False,
                "message": f"蜂鸟下单失败: {error_msg}"
            }

    except Exception as e:
        logger.error(f"调用蜂鸟下单接口异常，订单ID: {delivery_order.id}, 异常: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"蜂鸟下单异常: {str(e)}"
        }


async def _initiate_refund_for_delivery_fee_change(db: Session, delivery_order, paid_amount: float) -> None:
    """
    因配送费变动发起退款

    Args:
        db: 数据库会话
        delivery_order: 外卖订单对象
        paid_amount: 已支付金额（元）
    """
    try:
        logger.info(f"因配送费变动发起退款，订单ID: {delivery_order.id}, 退款金额: {paid_amount}")

        # 这里应该调用微信退款接口
        # 由于退款逻辑比较复杂，这里只记录日志，实际实现需要调用微信退款API
        logger.warning(f"需要发起退款 - 订单ID: {delivery_order.id}, 订单号: {delivery_order.order_no}, 金额: {paid_amount}元")

        # 更新订单状态为已取消（因配送费变动）
        delivery_order.status = OrderStatus.CANCELLED
        db.commit()

    except Exception as e:
        logger.error(f"发起退款异常，订单ID: {delivery_order.id}, 异常: {str(e)}", exc_info=True)


def _add_delivery_mode_params_for_payment(order_data: dict) -> None:
    """
    根据配置的配送模式添加相应参数到蜂鸟订单数据中（支付模块专用）

    Args:
        order_data: 蜂鸟订单数据字典，会被直接修改
    """
    # 根据配置的配送模式添加相应参数
    delivery_mode = settings.FENGNIAO_DELIVERY_MODE.lower()

    if delivery_mode == "store":
        # 门店发单模式：使用out_shop_code或chain_store_id
        if settings.FENGNIAO_CHAIN_STORE_ID:
            order_data["chain_store_id"] = int(settings.FENGNIAO_CHAIN_STORE_ID)
            logger.info(f"使用门店发单模式 - chain_store_id: {settings.FENGNIAO_CHAIN_STORE_ID}")
        else:
            order_data["out_shop_code"] = settings.FENGNIAO_SHOP_ID
            logger.info(f"使用门店发单模式 - out_shop_code: {settings.FENGNIAO_SHOP_ID}")

    elif delivery_mode == "point_to_point":
        # 点对点配送模式：添加取货点信息
        order_data.update({
            "transport_longitude": settings.FENGNIAO_TRANSPORT_LONGITUDE,  # 取货点经度
            "transport_latitude": settings.FENGNIAO_TRANSPORT_LATITUDE,   # 取货点纬度
            "transport_address": settings.FENGNIAO_TRANSPORT_ADDRESS,  # 取货点地址描述
            "transport_tel": settings.FENGNIAO_TRANSPORT_TEL,    # 取货点联系电话
        })
        logger.info(f"使用点对点配送模式")
        logger.info(f"  - 取货点坐标: ({settings.FENGNIAO_TRANSPORT_LONGITUDE}, {settings.FENGNIAO_TRANSPORT_LATITUDE})")
        logger.info(f"  - 取货点地址: {settings.FENGNIAO_TRANSPORT_ADDRESS}")
        logger.info(f"  - 取货点电话: {settings.FENGNIAO_TRANSPORT_TEL}")

    else:
        # 默认使用门店发单模式
        order_data["out_shop_code"] = settings.FENGNIAO_SHOP_ID
        logger.warning(f"未知的配送模式: {delivery_mode}，默认使用门店发单模式")
        logger.info(f"使用门店发单模式 - out_shop_code: {settings.FENGNIAO_SHOP_ID}")
