# -*- coding: utf-8 -*-
# 订单模块

from fastapi import APIRouter, Depends, HTTPException, Header, File, UploadFile, Request
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import re

from app.dao.account import account_transaction_dao, account_dao
from app.models.enum import Status
from app.service.wechat_miniapp.wx_reservation import reservation_service
from app.core.config import settings
from app.service.wechat_miniapp.wx_service import WechatService
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.service.user import UserService
from app.service.wechat_miniapp.wx_order import WXOrderService
from app.models.user import PersonalUser
from app.dao.rule import rule_dao
from app.models.order import Order
from app.core.deps import get_current_user, get_db
from app.service.wechat_miniapp.wx_account import AccountService
import logging
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from app.schemas.user import PersonalUserCreateWX
import random
import string
import os
from pathlib import Path

from app.utils.common import get_phone_number, product_wx_token, allowed_file, secure_filename
from app.models.account import AccountType, TransactionType
from app.schemas.order import OrderUpdate
from app.models.order import PaymentStatus, OrderType, PaymentMethod
from app.dao.order import order_dao, order_item_dao
from app.service.wechat_miniapp.wx_service import wechat_service
from app.service.payment import payment_service
from app.dao.reservation import reservation_request_dao
from app.models.reservation import ReservationStatus
from app.models.order import OrderStatus, OrderItem
from app.dao.wx_payment import wx_payment_dao, wx_payment_service
from app.models.order import WxPaymentRecord, WxRefundRecord, WxRefundStatus, WxPaymentStatus
from app.schemas.account import AccountTransactionCreate
from app.utils.common import get_current_time
from app.dao.user import personal_user_dao
from app.dao.product import product_dao
from app.service.order import order_service
from app.utils.logger import logger
from app.dao.rule import rule_item_dao
from app.service.revervation import reservation_service as revervation_service
from app.service.order_cancel_service import OrderCancelService
from app.dao.reservation import biz_reservation_request_dao
from app.schemas.reservation import BizReservationRequestCreate
from app.dao.menu import menu_dao
from app.models.reservation import ReservationType
from app.models.rule import DiningReservationRule, RuleItem, RuleScope, RuleType, RuleOrderType
from app.models.product import MealType
from app.models.reservation import BizReservationRequest
from app.dao.user import enterprise_user_relation_dao, enterprise_dao
from app.dao.admin import admin_dao
from app.service.pricing import pricing_service
from app.schemas.order import OrderItemBase
from app.events.deps import EventBusDep
from app.events.models import OrderEvent, OrderEventAction
from app.core.scheduler import send_order_change_reminders_task
from app.service.fengniao import FengniaoClient
from app.core.config import settings

router = APIRouter()


@router.post("/order/pay/create")
async def create_order_pay(task_info: dict, event_bus: EventBusDep, token: Optional[str] = Header(None), db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    创建订单支付
    """
    try:
        logger.info(f"开始创建订单支付，接收到的参数: {task_info}")

        # 验证用户token
        logger.info(f"开始验证用户token: {token[:10] if token else 'None'}")
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 验证订单ID
        if not task_info["order_no"]:
            logger.error("订单号为空")
            raise HTTPException(
                status_code=400,
                detail={"message": "订单号不能为空", "code": 400}
            )

        # 验证充值金额
        amount = task_info.get("amount")
        if amount is None or amount < 0:
            logger.error(f"充值金额无效: {amount}（不能为空且不能小于0）")
            raise HTTPException(
                status_code=400,
                detail={"message": "充值金额不能为空且不能小于0", "code": 400}
            )

        logger.info(f"充值金额验证通过: {task_info['amount']}")

        # 获取订单
        logger.info(f"开始获取订单，订单号: {task_info['order_no']}")
        order = order_dao.get_by_order_no(db, task_info["order_no"])

        # 检查订单是否存在
        if not order:
            logger.error("订单不存在")
            raise HTTPException(
                status_code=500,
                detail={"message": "订单不存在", "code": 500}
            )
        logger.info(f"订单存在，订单号: {order.order_no}")

        # 立即保存订单ID和订单号，避免后续访问失效的实例
        order_id = order.id
        order_no = order.order_no
        order_user_id = order.user_id
        order_payable_amount = order.payable_amount
        order_status = order.status
        order_payment_status = order.payment_status

        # 更改订单类型
        # TODO: 检查去掉update_order_type的影响
        # order_dao.update_order_type(db, order_id, OrderType.RESERVATION)

        # 更新订单支付方式
        # 使用微信支付流程
        if task_info["paymentMethod"] == "wxpay":
            if amount <= 0:
                logger.error(f"支付金额为0，无需支付")
                raise HTTPException(
                    status_code=400,
                    detail={"message": "支付金额为0，无需支付", "code": 400}
                )
            order_dao.update_payment_method(db, order_id, PaymentMethod.WECHAT_PAY)

            # 微信支付则在小程序端支付完后，回调数据进行更新，不在此次更新。
            logger.info(f"订单支付方式更新成功: 微信支付")

            # 并没有实现支付，而是只是生成支付参数，为微信支付做准备
            # 生成充值订单支付参数
            logger.info("开始生成微信支付参数")
            try:
                # task_info["amount"] = 0.01 # 测试用
                class_wechat_service = WechatService()
                pay_params = class_wechat_service.create_jsapi_payment(
                    user.wechat_id,
                    order_no,
                    order_payable_amount,
                    "订单支付"
                )
                logger.info(f"微信支付参数生成成功: {pay_params}")
            except Exception as e:
                logger.error(f"生成微信支付参数失败: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail={"message": f"生成支付参数失败: {str(e)}", "code": 500}
                )

            return {
                "message": "创建成功",
                "status": 200,
                "payParams": pay_params
            }

        # 使用个人账户余额支付流程
        elif task_info["paymentMethod"] == "balance":
            order_dao.update_payment_method(db, order_id, PaymentMethod.ACCOUNT_BALANCE)
            # 进行余额扣款
            logger.info(f"余额扣款成功，扣款金额: {task_info['amount']}")
            payment_info = {
                "payment_method": PaymentMethod.ACCOUNT_BALANCE
            }
            paid_order = payment_service.pay_order(db, order_id, payment_info)
            # 个人账户余额支付成功处理
            if paid_order:
                logger.info(f"余额支付成功，支付金额: {task_info['amount']}")

                # 更新产品库存 - 使用原始订单ID
                order_service.update_product_stock(db, order_id)
                logger.info(f"产品库存更新成功，订单ID: {order_id}")

                # 检查是否是外卖订单，如果是则调用蜂鸟下单接口
                if order.type == OrderType.DELIVERY:
                    logger.info(f"检测到外卖订单，开始调用蜂鸟下单接口，订单ID: {order_id}")
                    try:
                        # 获取外卖订单详细信息
                        delivery_order = order_dao.get(db, order_id)
                        if hasattr(delivery_order, 'delivery_fee') and hasattr(delivery_order, 'service_goods_id'):
                            # 调用蜂鸟下单接口
                            fengniao_result = await _create_fengniao_order(db, delivery_order)
                            if not fengniao_result["success"]:
                                logger.error(f"蜂鸟下单失败: {fengniao_result['message']}")
                                # 如果是配送费变动，返回特定错误
                                if "配送费发生变动" in fengniao_result["message"]:
                                    raise HTTPException(
                                        status_code=400,
                                        detail={"message": "配送费发生变动，支付失败", "code": 400}
                                    )
                                else:
                                    raise HTTPException(
                                        status_code=500,
                                        detail={"message": f"蜂鸟下单失败: {fengniao_result['message']}", "code": 500}
                                    )
                            else:
                                logger.info(f"蜂鸟下单成功，订单ID: {order_id}, 蜂鸟订单号: {fengniao_result.get('fengniao_order_id')}")
                        else:
                            logger.warning(f"外卖订单缺少必要的蜂鸟参数，订单ID: {order_id}")
                    except HTTPException:
                        raise
                    except Exception as e:
                        logger.error(f"调用蜂鸟下单接口异常: {str(e)}", exc_info=True)
                        raise HTTPException(
                            status_code=500,
                            detail={"message": f"蜂鸟下单异常: {str(e)}", "code": 500}
                        )

                # TODO：个人支付成功，虚拟产品交付
                # 发布订单支付成功事件(余额支付)事件
                try:
                    order_event = OrderEvent(
                        action=OrderEventAction.PAID,
                        order_id=order_id,
                        order_no=order_no,
                        user_id=order_user_id,
                        amount=order_payable_amount,  # 转换为元
                        status=str(order_status),
                        payment_status=str(order_payment_status),
                        source="miniapp_order_payment_by_balance",
                        type=order.type.value
                    )
                    await event_bus.publish(order_event)
                    logger.info(f"订单支付成功事件(余额支付)已发布: {order_event.id}")
                except Exception as e:
                    logger.error(f"发布订单支付成功事件(余额支付)失败: {e}")

                return {
                    "message": "余额支付成功",
                    "status": 200
                }
            else:
                logger.error("余额支付失败")
                raise HTTPException(
                    status_code=500,
                    detail={"message": "余额支付失败", "code": 500}
                )

        # 使用企业账户余额支付流程
        elif task_info["paymentMethod"] == "enterprise":
            if not task_info["enterprise_id"]:
                logger.error("企业ID为空")
                return {
                    "message": "找不到关联企业",
                    "status": 400
                }

            # TODO：一般产品支付跳过该环节
            # 检查当日是否存在同一类型的支付
            # 一般产品购买需要跳过该环节

            # 餐食重复订单检查企业自助餐只允许预订1天
            # 标记订单消费日期
            order_item_list = order_item_dao.get_by_order(db, order_id)
            # 按餐食类型检查是否存在重复订单
            conflict_info = []
            if order_item_list:
                for tmp_order_item in order_item_list:
                    logger.info(f"订单项: {tmp_order_item}")
                    tmp_reservation_request = reservation_request_dao.get_by_order_item_id_and_user_id(db,
                                                                                                       tmp_order_item.id,
                                                                                                       user.id)
                    reservation_period = tmp_reservation_request.reservation_period
                    reservation_period_start = reservation_period.split("_")[0]
                    reservation_period_start = '20' + reservation_period_start[0:6]
                    reservation_period_start = datetime.strptime(reservation_period_start, '%Y%m%d')
                    request_date_list = [reservation_period_start.date()]

                    # 获取该预订请求的餐食类型
                    rule_item = rule_item_dao.get(db, tmp_reservation_request.rule_item_id)
                    meal_type = rule_item.meal_type if rule_item else None

                    logger.info(
                        f"=== 支付检查 ===: 检查日期 {reservation_period_start.date()} 的餐食类型 {meal_type.value if meal_type else '未知'}")

                    # 查询该日期该餐食类型是否已经存在企业账户支付的订单
                    has_enterprise_order, existing_reservation_period_start = order_dao.check_enterprise_order_in_day_by_meal_type(
                        db,
                        user.id,
                        request_date_list,
                        meal_type
                    )

                    if has_enterprise_order:
                        conflict_date = existing_reservation_period_start.strftime('%Y-%m-%d')
                        meal_type_name = meal_type.value if meal_type else '未知'
                        conflict_info.append(f"{conflict_date} 的 {meal_type_name}")
                        logger.error(f"用户当日该餐食类型({meal_type_name})已使用企业账户支付，不允许重复支付")

            if conflict_info:
                return {
                    "message": f"存在重复订单，请修改消费日期: {', '.join(conflict_info)} 的订单",
                    "status": 400
                }
            else:
                logger.info(f"用户当日各餐食类型均未使用企业账户支付，允许支付")

            order_dao.update_payment_method(db, order_id, PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE)
            logger.info(f"企业支付成功，支付金额: {task_info['amount']}")
            payment_info = {
                "payment_method": PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE,
                "enterprise_id": task_info["enterprise_id"]
            }
            paid_order = payment_service.pay_order(db, order_id, payment_info)

            # 支付成功
            if paid_order:
                logger.info(f"企业支付成功，支付金额: {task_info['amount']}")
                paid_order_id = paid_order.id
                paid_order_no = paid_order.order_no
                paid_order_user_id = paid_order.user_id
                paid_order_payable_amount = paid_order.payable_amount
                paid_order_status = paid_order.status
                paid_order_payment_status = paid_order.payment_status

                # 更新产品库存 - 使用原始订单ID
                order_service.update_product_stock(db, order_id)
                logger.info(f"产品库存更新成功，订单ID: {order_id}")

                # 检查是否是外卖订单，如果是则调用蜂鸟下单接口
                if order.type == OrderType.DELIVERY:
                    logger.info(f"检测到外卖订单，开始调用蜂鸟下单接口，订单ID: {order_id}")
                    try:
                        # 获取外卖订单详细信息
                        delivery_order = order_dao.get(db, order_id)
                        if hasattr(delivery_order, 'delivery_fee') and hasattr(delivery_order, 'service_goods_id'):
                            # 调用蜂鸟下单接口
                            fengniao_result = await _create_fengniao_order(db, delivery_order)
                            if not fengniao_result["success"]:
                                logger.error(f"蜂鸟下单失败: {fengniao_result['message']}")
                                # 如果是配送费变动，返回特定错误
                                if "配送费发生变动" in fengniao_result["message"]:
                                    raise HTTPException(
                                        status_code=400,
                                        detail={"message": "配送费发生变动，支付失败", "code": 400}
                                    )
                                else:
                                    raise HTTPException(
                                        status_code=500,
                                        detail={"message": f"蜂鸟下单失败: {fengniao_result['message']}", "code": 500}
                                    )
                            else:
                                logger.info(f"蜂鸟下单成功，订单ID: {order_id}, 蜂鸟订单号: {fengniao_result.get('fengniao_order_id')}")
                        else:
                            logger.warning(f"外卖订单缺少必要的蜂鸟参数，订单ID: {order_id}")
                    except HTTPException:
                        raise
                    except Exception as e:
                        logger.error(f"调用蜂鸟下单接口异常: {str(e)}", exc_info=True)
                        raise HTTPException(
                            status_code=500,
                            detail={"message": f"蜂鸟下单异常: {str(e)}", "code": 500}
                        )

                # TODO：企业支付成功，虚拟产品交付
                # 如果是虚拟产品，执行交付

                # 发布订单支付成功事件(企业支付)事件
                try:
                    order_event = OrderEvent(
                        action=OrderEventAction.PAID,
                        order_id=paid_order_id,
                        order_no=paid_order_no,
                        user_id=paid_order_user_id,
                        amount=paid_order_payable_amount,  # 转换为元
                        status=str(paid_order_status),
                        payment_status=str(paid_order_payment_status),
                        source="miniapp_order_payment_by_enterprise",
                        type=order.type.value
                    )
                    await event_bus.publish(order_event)
                    logger.info(f"订单支付成功事件(企业支付)已发布: {order_event.id}")
                except Exception as e:
                    logger.error(f"发布订单支付成功事件(企业支付)失败: {e}")

                return {
                    "message": "企业支付成功",
                    "status": 200
                }
            else:
                logger.error("企业支付失败")
                raise HTTPException(
                    status_code=500,
                    detail={"message": "企业支付失败", "code": 500}
                )

        # 商务餐企业支付流程(只允许企业管理员进行支付)
        elif task_info["paymentMethod"] == "biz_enterprise":
            # 商务餐企业支付
            if not task_info["enterprise_id"]:
                logger.error("企业ID为空")
                return {
                    "message": "找不到关联企业",
                    "status": 400
                }
            # 判断该用户是否是该企业的管理员
            enterprise = enterprise_dao.get(db, task_info["enterprise_id"])
            if not enterprise:
                logger.error("企业不存在")
                return {
                    "message": "企业不存在",
                    "status": 400
                }

            # 获取用户与企业的关系
            enterprise_relation = enterprise_user_relation_dao.get_by_personal_user_id(db, user.id)
            is_admin = False
            for relation in enterprise_relation:
                if relation.enterprise_id == task_info["enterprise_id"] and relation.is_admin:
                    is_admin = True
                    break

            if not is_admin:
                logger.error("用户不是企业管理员")
                return {
                    "message": "用户不是企业管理员",
                    "status": 400
                }

            order_dao.update_payment_method(db, order_id, PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE)
            logger.info(f"商务餐企业支付成功，支付金额: {task_info['amount']}")
            payment_info = {
                "payment_method": PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE,
                "enterprise_id": task_info["enterprise_id"],
                "type": "biz_enterprise"
            }
            paid_order = payment_service.pay_order(db, order_id, payment_info)
            if paid_order:
                logger.info(f"企业支付成功，支付金额: {task_info['amount']}")
                paid_order_id = paid_order.id
                paid_order_no = paid_order.order_no
                paid_order_user_id = paid_order.user_id
                paid_order_payable_amount = paid_order.payable_amount
                paid_order_status = paid_order.status
                paid_order_payment_status = paid_order.payment_status

                # 更新产品库存 - 使用原始订单ID
                order_service.update_product_stock(db, order_id)
                logger.info(f"产品库存更新成功，订单ID: {order_id}")

                # 检查是否是外卖订单，如果是则调用蜂鸟下单接口
                if order.type == OrderType.DELIVERY:
                    logger.info(f"检测到外卖订单，开始调用蜂鸟下单接口，订单ID: {order_id}")
                    try:
                        # 获取外卖订单详细信息
                        delivery_order = order_dao.get(db, order_id)
                        if hasattr(delivery_order, 'delivery_fee') and hasattr(delivery_order, 'service_goods_id'):
                            # 调用蜂鸟下单接口
                            fengniao_result = await _create_fengniao_order(db, delivery_order)
                            if not fengniao_result["success"]:
                                logger.error(f"蜂鸟下单失败: {fengniao_result['message']}")
                                # 如果是配送费变动，返回特定错误
                                if "配送费发生变动" in fengniao_result["message"]:
                                    raise HTTPException(
                                        status_code=400,
                                        detail={"message": "配送费发生变动，支付失败", "code": 400}
                                    )
                                else:
                                    raise HTTPException(
                                        status_code=500,
                                        detail={"message": f"蜂鸟下单失败: {fengniao_result['message']}", "code": 500}
                                    )
                            else:
                                logger.info(f"蜂鸟下单成功，订单ID: {order_id}, 蜂鸟订单号: {fengniao_result.get('fengniao_order_id')}")
                        else:
                            logger.warning(f"外卖订单缺少必要的蜂鸟参数，订单ID: {order_id}")
                    except HTTPException:
                        raise
                    except Exception as e:
                        logger.error(f"调用蜂鸟下单接口异常: {str(e)}", exc_info=True)
                        raise HTTPException(
                            status_code=500,
                            detail={"message": f"蜂鸟下单异常: {str(e)}", "code": 500}
                        )

                # TODO：企业支付成功，虚拟产品交付
                # 如果是虚拟产品，执行交付

                # 发布订单支付成功事件(企业支付)事件
                try:
                    order_event = OrderEvent(
                        action=OrderEventAction.PAID,
                        order_id=paid_order_id,
                        order_no=paid_order_no,
                        user_id=paid_order_user_id,
                        amount=paid_order_payable_amount,  # 转换为元
                        status=str(paid_order_status),
                        payment_status=str(paid_order_payment_status),
                        source="miniapp_biz_order_payment_by_enterprise",
                        type=order.type.value
                    )
                    await event_bus.publish(order_event)
                    logger.info(f"商务餐订单支付成功事件(企业支付)已发布: {order_event.id}")
                except Exception as e:
                    logger.error(f"发布商务餐订单支付成功事件(企业支付)失败: {e}")

                return {
                    "message": "企业支付成功",
                    "status": 200
                }
            else:
                logger.error("企业支付失败")
                raise HTTPException(
                    status_code=500,
                    detail={"message": "企业支付失败", "code": 500}
                )

        else:
            logger.error("支付方式不支持")
            return {
                "message": "支付方式不支持",
                "status": 400
            }

    except Exception as e:
        logger.error(f"创建订单支付失败: {str(e)}")
        return {
            "message": f"创建订单支付失败",
            "status": 500
        }


@router.post("/order/item/cancel")
async def cancel_order_item(task_info: dict, event_bus: EventBusDep, token: Optional[str] = Header(None), db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    取消订单中的单个子项

    Args:
        task_info: 包含order_item_id的请求数据
        event_bus: 形参
        token: 用户token
        db: 数据库会话
    """
    try:
        logger.info(f"[订单子项取消] 开始处理，接收到的参数: {task_info}")

        # 验证用户
        logger.info(f"[订单子项取消] 开始验证用户token: {token[:10] if token else 'None'}")
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.error(f"[订单子项取消] 用户token验证失败")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )
        user_id = user.id
        logger.info(f"[订单子项取消] 用户token验证成功，用户ID: {user_id}")

        # 获取订单项ID
        if "order_item_id" not in task_info or "order_no" not in task_info or "order_id" not in task_info:
            logger.error(f"[订单子项取消] 订单项ID为空")
            return {
                "message": "订单项ID不能为空",
                "status": 400
            }

        order_item_id = task_info["order_item_id"]
        order_no = task_info["order_no"]
        order_id = task_info["order_id"]

        # 调用 OrderCancelService 处理订单项取消
        logger.info(f"[订单子项取消] 调用 OrderCancelService.cancel 处理订单项取消")
        result = await OrderCancelService.cancel(
            db=db,
            order_no=order_no,
            order_id=order_id,
            order_item_id=order_item_id,
            user_id=user_id
        )

        # 如果取消成功，发布订单取消成功事件
        if result.get("status") == 200:
            try:
                # 获取订单信息用于事件发布
                order = order_dao.get(db, order_id)
                if order:
                    order_event = OrderEvent(
                        action=OrderEventAction.CANCELLED,
                        order_id=order_id,
                        order_no=order_no,
                        user_id=user_id,
                        amount=order.payable_amount,
                        status=str(order.status),
                        payment_status=str(order.payment_status),
                        source="miniapp_order_cancel",
                        additional_data={
                            "order_item_id": order_item_id
                        }
                    )
                    await event_bus.publish(order_event)
                    logger.info(f"订单取消成功事件已发布: {order_event.id}")
            except Exception as e:
                logger.error(f"订单取消成功事件发布失败: {e}")

        return result

    except Exception as e:
        logger.error(f"[订单子项取消] 处理异常: {str(e)}", exc_info=True)
        return {
            "message": f"取消订单项失败: {str(e)}",
            "status": 500
        }


async def _create_fengniao_order(db: Session, delivery_order) -> Dict[str, Any]:
    """
    调用蜂鸟下单接口创建配送订单

    Args:
        db: 数据库会话
        delivery_order: 外卖订单对象

    Returns:
        Dict: 包含成功状态和结果信息的字典
    """
    try:
        logger.info(f"开始调用蜂鸟下单接口，订单ID: {delivery_order.id}")

        # 检查必要的蜂鸟参数
        if not all([
            delivery_order.delivery_fee,
            delivery_order.service_goods_id,
            delivery_order.base_goods_id,
            delivery_order.t_index_id
        ]):
            logger.error(f"外卖订单缺少必要的蜂鸟参数，订单ID: {delivery_order.id}")
            return {
                "success": False,
                "message": "外卖订单缺少必要的蜂鸟参数"
            }

        # 构建蜂鸟下单请求数据
        order_data = {
            "partner_order_code": delivery_order.order_no,
            "receiver_primary_phone": delivery_order.delivery_address_raw.get("phone") if delivery_order.delivery_address_raw else "",
            "actual_delivery_amount_cent": int(delivery_order.delivery_fee * 100),  # 转换为分
            "goods_count": len(delivery_order.items),
            "service_goods_id": delivery_order.service_goods_id,
            "receiver_latitude": delivery_order.delivery_address_raw.get("latitude") if delivery_order.delivery_address_raw else 0,
            "goods_total_amount_cent": int(delivery_order.total_amount * 100),  # 转换为分
            "receiver_name": delivery_order.delivery_address_raw.get("name") if delivery_order.delivery_address_raw else "",
            "pre_create_order_t_index_id": delivery_order.t_index_id,
            "order_type": 1,  # 即时单
            "goods_weight": 1.0,  # 默认重量
            "goods_actual_amount_cent": int(delivery_order.total_amount * 100),  # 转换为分
            "receiver_longitude": delivery_order.delivery_address_raw.get("longitude") if delivery_order.delivery_address_raw else 0,
            "base_goods_id": delivery_order.base_goods_id,
            "receiver_address": delivery_order.delivery_address,
            "position_source": 3,  # 高德地图
            "goods_item_list": []
        }

        # 根据配置的配送模式添加相应参数
        _add_delivery_mode_params(order_data)

        # 构建商品列表
        for item in delivery_order.items:
            goods_item = {
                "item_actual_amount_cent": int(item.payable_amount * 100),  # 转换为分
                "item_amount_cent": int(item.price * item.quantity * 100),  # 转换为分
                "item_name": item.product.name if item.product else f"商品{item.product_id}",
                "item_id": str(item.product_id),
                "item_quantity": item.quantity
            }
            order_data["goods_item_list"].append(goods_item)

        # 调用蜂鸟API
        fengniao_client = FengniaoClient()
        result = fengniao_client.create_order(order_data)

        logger.info(f"蜂鸟下单接口返回结果: {result}")

        # 检查返回结果
        if result.get("code") == "200":
            # 解析蜂鸟订单ID
            business_data = result.get("business_data")
            if business_data:
                import json
                if isinstance(business_data, str):
                    business_data = json.loads(business_data)
                fengniao_order_id = business_data.get("order_id")

                if fengniao_order_id:
                    # 更新订单的蜂鸟订单号
                    delivery_order.fengniao_order_id = str(fengniao_order_id)
                    delivery_order.fengniao_status = 1  # 运单生成成功
                    db.commit()

                    logger.info(f"蜂鸟下单成功，订单ID: {delivery_order.id}, 蜂鸟订单号: {fengniao_order_id}")
                    return {
                        "success": True,
                        "message": "蜂鸟下单成功",
                        "fengniao_order_id": fengniao_order_id
                    }

        # 检查是否是价格不一致错误
        error_code = result.get("code", "")
        error_msg = result.get("msg", "")

        if error_code == "B0109":
            logger.error(f"蜂鸟下单失败 - 配送费发生变动，订单ID: {delivery_order.id}")
            return {
                "success": False,
                "message": "配送费发生变动，支付失败"
            }
        else:
            logger.error(f"蜂鸟下单失败，订单ID: {delivery_order.id}, 错误码: {error_code}, 错误信息: {error_msg}")
            return {
                "success": False,
                "message": f"蜂鸟下单失败: {error_msg}"
            }

    except Exception as e:
        logger.error(f"调用蜂鸟下单接口异常，订单ID: {delivery_order.id}, 异常: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"蜂鸟下单异常: {str(e)}"
        }


def _add_delivery_mode_params(order_data: dict) -> None:
    """
    根据配置的配送模式添加相应参数到蜂鸟订单数据中

    Args:
        order_data: 蜂鸟订单数据字典，会被直接修改
    """
    # 根据配置的配送模式添加相应参数
    delivery_mode = settings.FENGNIAO_DELIVERY_MODE.lower()

    if delivery_mode == "store":
        # 门店发单模式：使用out_shop_code或chain_store_id
        if settings.FENGNIAO_CHAIN_STORE_ID:
            order_data["chain_store_id"] = int(settings.FENGNIAO_CHAIN_STORE_ID)
            logger.info(f"使用门店发单模式 - chain_store_id: {settings.FENGNIAO_CHAIN_STORE_ID}")
        else:
            order_data["out_shop_code"] = settings.FENGNIAO_SHOP_ID
            logger.info(f"使用门店发单模式 - out_shop_code: {settings.FENGNIAO_SHOP_ID}")

    elif delivery_mode == "point_to_point":
        # 点对点配送模式：添加取货点信息
        order_data.update({
            "transport_longitude": settings.FENGNIAO_TRANSPORT_LONGITUDE,  # 取货点经度
            "transport_latitude": settings.FENGNIAO_TRANSPORT_LATITUDE,   # 取货点纬度
            "transport_address": settings.FENGNIAO_TRANSPORT_ADDRESS,  # 取货点地址描述
            "transport_tel": settings.FENGNIAO_TRANSPORT_TEL,    # 取货点联系电话
        })
        logger.info(f"使用点对点配送模式")
        logger.info(f"  - 取货点坐标: ({settings.FENGNIAO_TRANSPORT_LONGITUDE}, {settings.FENGNIAO_TRANSPORT_LATITUDE})")
        logger.info(f"  - 取货点地址: {settings.FENGNIAO_TRANSPORT_ADDRESS}")
        logger.info(f"  - 取货点电话: {settings.FENGNIAO_TRANSPORT_TEL}")

    else:
        # 默认使用门店发单模式
        order_data["out_shop_code"] = settings.FENGNIAO_SHOP_ID
        logger.warning(f"未知的配送模式: {delivery_mode}，默认使用门店发单模式")
        logger.info(f"使用门店发单模式 - out_shop_code: {settings.FENGNIAO_SHOP_ID}")
