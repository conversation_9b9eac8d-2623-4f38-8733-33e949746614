from typing import Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, Header
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.product import product_dao
from app.service.tag import tag_service
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.utils.logger import logger

router = APIRouter()


@router.get("/delivery/config")
async def get_delivery_config(
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取预约配置
    Args:
        token: 用户token
        db: 数据库会话
    Returns:
        Dict: 包含预约配置信息的响应
    """
    logger.info(f"开始外卖预订配置")
    user = WeChatUserService.verify_token(db, token)
    if not user:
        logger.warning(f"token无效或已过期: {token[:10]}...")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )
    logger.info(f"用户token验证成功，用户ID: {user.id}")

    lunch_rules = tag_service.get_rules_with_items_by_tag_name(db, 'delivery_lunch')
    lunch_product_ids = tag_service.get_product_ids_by_tag_name(db, 'delivery_lunch')
    diner_rules = tag_service.get_rules_with_items_by_tag_name(db, 'delivery_dinner')
    diner_product_ids = tag_service.get_product_ids_by_tag_name(db, 'delivery_dinner')
    extra_product_ids = tag_service.get_product_ids_by_tag_name(db, 'delivery_extra')

    # 根据extra_product_ids获取产品信息
    extra_products = []
    if extra_product_ids:
        for product_id in extra_product_ids:
            product = product_dao.get(db, product_id)
            if product:
                extra_products.append({
                    "id": product.id,
                    "name": product.name,
                    "price": product.price,
                    "description": product.description,
                    "image": product.image,
                    "stock": product.stock,
                    "status": product.status.value if product.status else "active",
                    "type": product.type.value if product.type else "product",
                    "meal_type": product.meal_type.value if product.meal_type else "buffet",
                    "listed_at": product.listed_at.strftime("%Y-%m-%d %H:%M:%S") if product.listed_at else None,
                    "created_at": product.created_at.strftime("%Y-%m-%d %H:%M:%S") if product.created_at else None,
                    "updated_at": product.updated_at.strftime("%Y-%m-%d %H:%M:%S") if product.updated_at else None
                })

    data = {
        "delivery_lunch": {
            "rules": [
                {
                    "rule_id": rule["id"],
                    "rule_name": rule["name"],
                    "rule_times": rule.get("rule_times", []),
                    "rule_items": rule["rule_items"]
                } for rule in lunch_rules
            ],
            "product_ids": lunch_product_ids,
        },
        "delivery_dinner": {
            "rules": [
                {
                    "rule_id": rule["id"],
                    "rule_name": rule["name"],
                    "rule_times": rule.get("rule_times", []),
                    "rule_items": rule["rule_items"]
                } for rule in diner_rules
            ],
            "product_ids": diner_product_ids,
        },
        "extra_products": extra_products
    }
    return {
        "code": 200,
        "message": "success",
        "data": data
    }
