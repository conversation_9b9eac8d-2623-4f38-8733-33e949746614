from datetime import datetime, timedelta
from typing import List, Dict, Any, Tu<PERSON>

from sqlalchemy.orm import Session

from app.dao.admin import get_notify_admins
from app.db.session import SessionLocal
from app.models.order import DeliveryOrder, OrderStatus, PaymentStatus
from app.utils.logger import logger
from app.tasks.notifications.utils import send_dingtalk_message, send_miniapp_message
from app.core.config import settings


def get_delivery_order_statistics(
    db: Session,
    start_time: datetime,
    end_time: datetime
) -> Tuple[int, float, float, float]:
    """
    获取外卖订单统计数据

    Args:
        db: 数据库会话
        start_time: 开始时间
        end_time: 结束时间

    Returns:
        Tuple[int, float, float, float]: (订单数量, 订单总金额, 配送费总额, 实际支付总额)
    """
    try:
        # 查询指定时间范围内已支付的外卖订单
        orders = db.query(DeliveryOrder).filter(
            DeliveryOrder.created_at >= start_time,
            DeliveryOrder.created_at <= end_time,
            DeliveryOrder.payment_status == PaymentStatus.PAID
        ).all()

        # 统计数据
        order_count = len(orders)
        total_amount = sum(order.total_amount for order in orders)
        total_delivery_fee = sum(order.delivery_fee for order in orders)
        total_paid_amount = sum(order.actual_amount_paid for order in orders)

        logger.info(f"外卖订单统计 - 时间范围: {start_time} 至 {end_time}")
        logger.info(f"  订单数量: {order_count}")
        logger.info(f"  订单总金额: {total_amount:.2f}")
        logger.info(f"  配送费总额: {total_delivery_fee:.2f}")
        logger.info(f"  实际支付总额: {total_paid_amount:.2f}")

        return order_count, total_amount, total_delivery_fee, total_paid_amount

    except Exception as e:
        logger.error(f"获取外卖订单统计数据失败: {e}")
        return 0, 0.0, 0.0, 0.0


def classify_delivery_statistics_by_time(
    db: Session,
    start_time: datetime,
    end_time: datetime
) -> Dict[str, Dict[str, Any]]:
    """
    按时间段分类统计外卖订单数据

    Args:
        db: 数据库会话
        start_time: 开始时间
        end_time: 结束时间

    Returns:
        Dict[str, Dict[str, Any]]: 包含lunch和dinner统计数据的字典
    """
    # 初始化统计结果
    statistics = {
        "lunch": {
            "count": 0,
            "total_amount": 0.0,
            "delivery_fee": 0.0,
            "paid_amount": 0.0
        },
        "dinner": {
            "count": 0,
            "total_amount": 0.0,
            "delivery_fee": 0.0,
            "paid_amount": 0.0
        }
    }

    try:
        # 查询指定时间范围内已支付的外卖订单
        orders = db.query(DeliveryOrder).filter(
            DeliveryOrder.created_at >= start_time,
            DeliveryOrder.created_at <= end_time,
            DeliveryOrder.payment_status == PaymentStatus.PAID
        ).all()

        for order in orders:
            # 根据订单创建时间判断是午餐还是晚餐
            # 午餐时段: 10:00-16:00
            # 晚餐时段: 16:00-22:00
            order_hour = order.created_at.hour

            if 10 <= order_hour < 16:
                meal_type = "lunch"
            elif 16 <= order_hour < 22:
                meal_type = "dinner"
            else:
                # 其他时间段不统计
                continue

            # 累加统计数据
            statistics[meal_type]["count"] += 1
            statistics[meal_type]["total_amount"] += order.total_amount
            statistics[meal_type]["delivery_fee"] += order.delivery_fee
            statistics[meal_type]["paid_amount"] += order.actual_amount_paid

        logger.info(f"外卖订单分类统计完成:")
        logger.info(f"  午餐订单: {statistics['lunch']['count']} 单, 金额: {statistics['lunch']['total_amount']:.2f}")
        logger.info(f"  晚餐订单: {statistics['dinner']['count']} 单, 金额: {statistics['dinner']['total_amount']:.2f}")

        return statistics

    except Exception as e:
        logger.error(f"分类统计外卖订单数据失败: {e}")
        return statistics


async def send_daily_delivery_statistic_notification(meal_type: str, day_type: str):
    """
    发送每日外卖订单统计通知

    Args:
        meal_type: 餐次类型，"lunch"或"dinner"
        day_type: 日期类型，"today"或"tomorrow"
    """
    # 检查是否启用通知功能
    if not settings.ENABLE_NOTIFICATION:
        logger.info("通知功能已禁用，跳过发送外卖订单统计通知")
        return

    # 微信小程序订阅消息模板ID
    template_id = "WYycb9FEm8qgpPHpo1XTYejQp_2H7Wilsx1IGrB4GDo"

    db = SessionLocal()
    try:
        # 获取通知用户列表
        notify_users = get_notify_admins(db)

        # 根据day_type计算目标日期
        if day_type.lower() == "today":
            target_date = datetime.now().date()
        elif day_type.lower() == "tomorrow":
            target_date = (datetime.now() + timedelta(days=1)).date()
        else:
            logger.error(f"不支持的day_type: {day_type}")
            return

        # 生成当天的开始和结束时间
        start_time = datetime.combine(target_date, datetime.min.time())
        end_time = datetime.combine(target_date, datetime.max.time())
        target_date_str = target_date.strftime("%Y年%m月%d日")

        # 获取统计数据
        order_count, total_amount, total_delivery_fee, total_paid_amount = get_delivery_order_statistics(
            db, start_time, end_time
        )

        # 获取分类统计数据
        classify_result = classify_delivery_statistics_by_time(db, start_time, end_time)

        # 根据meal_type和day_type发送不同的通知
        if meal_type.lower() == "lunch" and day_type.lower() == "today":
            logger.info(f"今日外卖午餐订单统计：{classify_result['lunch']['count']} 单 {datetime.now()}")

            # 发送微信小程序消息
            await send_miniapp_message(
                recipients=notify_users,
                template_id=template_id,
                template_data={
                    "time11": {"value": target_date_str},
                    "thing12": {"value": "今日外卖午餐订单数"},
                    "character_string14": {"value": str(classify_result['lunch']['count'])}
                }
            )

            # 发送钉钉消息
            notification_text = f"️> 订单数: **{classify_result['lunch']['count']}** 单\n"
            notification_text += f"> 订单金额: **{classify_result['lunch']['total_amount']:.2f}** 元\n"
            notification_text += f"> 配送费: **{classify_result['lunch']['delivery_fee']:.2f}** 元"

            result = await send_dingtalk_message(
                title="今日外卖午餐订单统计",
                body=notification_text
            )

        elif meal_type.lower() == "dinner" and day_type.lower() == "today":
            logger.info(f"今日外卖晚餐订单统计：{classify_result['dinner']['count']} 单 {datetime.now()}")

            # 发送微信小程序消息
            await send_miniapp_message(
                recipients=notify_users,
                template_id=template_id,
                template_data={
                    "time11": {"value": target_date_str},
                    "thing12": {"value": "今日外卖晚餐订单数"},
                    "character_string14": {"value": str(classify_result['dinner']['count'])}
                }
            )

            # 发送钉钉消息
            notification_text = f"️> 订单数: **{classify_result['dinner']['count']}** 单\n"
            notification_text += f"> 订单金额: **{classify_result['dinner']['total_amount']:.2f}** 元\n"
            notification_text += f"> 配送费: **{classify_result['dinner']['delivery_fee']:.2f}** 元"

            result = await send_dingtalk_message(
                title="今日外卖晚餐订单统计",
                body=notification_text
            )

        elif meal_type.lower() == "lunch" and day_type.lower() == "tomorrow":
            logger.info(f"明日外卖午餐订单统计：{classify_result['lunch']['count']} 单 {datetime.now()}")

            # 发送微信小程序消息
            await send_miniapp_message(
                recipients=notify_users,
                template_id=template_id,
                template_data={
                    "time11": {"value": target_date_str},
                    "thing12": {"value": "明日外卖午餐订单数"},
                    "character_string14": {"value": str(classify_result['lunch']['count'])}
                }
            )

            # 发送钉钉消息
            notification_text = f"️> 订单数: **{classify_result['lunch']['count']}** 单\n"
            notification_text += f"> 订单金额: **{classify_result['lunch']['total_amount']:.2f}** 元\n"
            notification_text += f"> 配送费: **{classify_result['lunch']['delivery_fee']:.2f}** 元"

            result = await send_dingtalk_message(
                title="明日外卖午餐订单统计",
                body=notification_text
            )

        elif meal_type.lower() == "dinner" and day_type.lower() == "tomorrow":
            logger.info(f"明日外卖晚餐订单统计：{classify_result['dinner']['count']} 单 {datetime.now()}")

            # 发送微信小程序消息
            await send_miniapp_message(
                recipients=notify_users,
                template_id=template_id,
                template_data={
                    "time11": {"value": target_date_str},
                    "thing12": {"value": "明日外卖晚餐订单数"},
                    "character_string14": {"value": str(classify_result['dinner']['count'])}
                }
            )

            # 发送钉钉消息
            notification_text = f"️> 订单数: **{classify_result['dinner']['count']}** 单\n"
            notification_text += f"> 订单金额: **{classify_result['dinner']['total_amount']:.2f}** 元\n"
            notification_text += f"> 配送费: **{classify_result['dinner']['delivery_fee']:.2f}** 元"

            result = await send_dingtalk_message(
                title="明日外卖晚餐订单统计",
                body=notification_text
            )

        else:
            logger.warning(f"未知的meal_type和day_type组合: {meal_type}, {day_type}")
            return

        if result.success_count > 0:
            logger.info("      钉钉通知发送成功")
        else:
            logger.error("      钉钉通知发送失败")

    except Exception as e:
        logger.error(f"发送外卖订单统计消息失败: {e}")
    finally:
        db.close()

