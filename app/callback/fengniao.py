import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import APIRouter, Request, Depends
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.models.order import OrderStatus

fengniao_router = APIRouter()
logger = logging.getLogger(__name__)


def _map_fengniao_status_to_order_status(fengniao_status: int) -> Optional[OrderStatus]:
    """将蜂鸟订单状态映射到系统订单状态

    蜂鸟订单状态说明：
    0: 订单生成
    1: 运单生成成功
    20: 骑手接单
    80: 骑手到店
    2: 配送中
    3: 已完成
    4: 已取消
    5: 配送异常

    Args:
        fengniao_status: 蜂鸟订单状态

    Returns:
        对应的系统订单状态，如果无法映射返回None
    """
    status_mapping = {
        0: None,  # 订单生成 - 保持原状态
        1: OrderStatus.PAID,  # 运单生成成功 - 已支付
        20: OrderStatus.ACCEPTED,  # 骑手接单 - 已接单
        80: OrderStatus.ARRIVED,  # 骑手到店 - 已备餐
        2: OrderStatus.PICKED,  # 配送中 - 已取餐
        3: OrderStatus.COMPLETED,  # 已完成 - 已完成
        4: OrderStatus.CANCELLED,  # 已取消 - 已取消
        5: OrderStatus.CANCELLED,  # 配送异常 - 已取消
    }

    return status_mapping.get(fengniao_status)


def _parse_business_data(business_data_str: str) -> Dict[str, Any]:
    """解析business_data字符串"""
    try:
        if isinstance(business_data_str, str):
            return json.loads(business_data_str)
        return business_data_str
    except json.JSONDecodeError as e:
        logger.error(f"解析business_data失败: {e}, 原始数据: {business_data_str}")
        return {}


def _save_callback_to_db(session: Session, callback_data: Dict[str, Any]) -> Optional[int]:
    """保存回调数据到数据库

    Args:
        session: 数据库会话
        callback_data: 回调数据

    Returns:
        回调记录ID，如果保存失败返回None
    """
    try:
        # 延迟导入避免循环导入
        from app.dao.fengniao import fengniao_callback_dao
        # 解析business_data
        business_data = _parse_business_data(callback_data.get("business_data", "{}"))
        callback_type = business_data.get("callback_business_type", "unknown")

        # 转换时间戳为可读格式
        timestamp = callback_data.get("timestamp", 0)
        try:
            timestamp_int = int(timestamp)
            update_time = datetime.fromtimestamp(timestamp_int / 1000)  # 毫秒转秒
        except (ValueError, TypeError):
            update_time = datetime.now()
            logger.warning(f"时间戳转换失败，使用当前时间: {timestamp}")

        # 保存到fengniao_callbacks表
        callback_record_data = {
            "app_id": callback_data.get("app_id", ""),
            "timestamp": timestamp_int if 'timestamp_int' in locals() else 0,
            "update_time": update_time,
            "signature": callback_data.get("signature", ""),
            "business_data": business_data,
            "callback_type": callback_type,
            "raw_data": callback_data
        }

        callback_record = fengniao_callback_dao.create_callback(session, callback_record_data)
        logger.info(f"保存回调记录成功，ID: {callback_record.id}, 类型: {callback_type}")

        return callback_record.id

    except Exception as e:
        logger.error(f"保存回调数据到数据库失败: {e}")
        return None


def _save_order_status_callback(session: Session, callback_id: int, business_data: Dict[str, Any]) -> bool:
    """保存订单状态回调详细信息

    Args:
        session: 数据库会话
        callback_id: 回调记录ID
        business_data: 业务数据

    Returns:
        是否保存成功
    """
    try:
        # 延迟导入避免循环导入
        from app.dao.fengniao import fengniao_callback_order_status_dao
        param = business_data.get("param", {})

        # 提取订单状态回调的所有字段
        order_data = {
            "order_id": param.get("order_id"),
            "app_id": param.get("app_id"),
            "partner_order_code": param.get("partner_order_code"),
            "order_status": param.get("order_status"),
            "carrier_driver_id": param.get("carrier_driver_id"),
            "carrier_driver_name": param.get("carrier_driver_name"),
            "carrier_driver_phone": param.get("carrier_driver_phone"),
            "carrier_lat": param.get("carrier_lat"),
            "carrier_lng": param.get("carrier_lng"),
            "description": param.get("description"),
            "detail_description": param.get("detail_description"),
            "error_code": param.get("error_code"),
            "error_scene": param.get("error_scene"),
            "push_time": param.get("push_time"),
            "transfer": param.get("transfer"),
            "api_code": param.get("apiCode"),
            "api_msg": param.get("apiMsg"),
            "complete_pics": param.get("complete_pics"),
            "state": param.get("state"),
            "shipping_order_id": param.get("shipping_order_id"),
            "reverse_shipping_order_id": param.get("reverse_shipping_order_id")
        }

        # 获取蜂鸟订单号
        fengniao_order_id = order_data.get("order_id")

        # 检查是否已存在该order_id的记录
        existing_records = None
        if fengniao_order_id:
            existing_records = fengniao_callback_order_status_dao.get_by_fengniao_order_id(
                session, fengniao_order_id
            )

        if existing_records:
            # 如果存在记录，更新最新的一条（列表第一条，因为按created_at desc排序）
            latest_record = existing_records[0]
            order_status_record = fengniao_callback_order_status_dao.update_order_status_callback(
                session, latest_record.id, order_data
            )
            logger.info(f"更新订单状态回调详细信息成功，ID: {order_status_record.id}, 蜂鸟订单号: {fengniao_order_id}")
        else:
            # 如果不存在记录，创建新记录
            order_status_record = fengniao_callback_order_status_dao.create_order_status_callback(
                session, callback_id, order_data
            )
            logger.info(f"创建订单状态回调详细信息成功，ID: {order_status_record.id}, 蜂鸟订单号: {fengniao_order_id}")

        return True

    except Exception as e:
        logger.error(f"保存订单状态回调详细信息失败: {e}")
        return False


def _update_delivery_order_status(session: Session, business_data: Dict[str, Any]) -> bool:
    """根据蜂鸟回调更新外卖订单状态

    Args:
        session: 数据库会话
        business_data: 业务数据

    Returns:
        是否更新成功
    """
    try:
        # 延迟导入避免循环导入
        from app.dao.order import order_dao
        from app.models.order import DeliveryOrder

        param = business_data.get("param", {})
        partner_order_code = param.get("partner_order_code")
        fengniao_order_id = param.get("order_id")
        fengniao_status = param.get("order_status")

        if not partner_order_code:
            logger.warning("回调数据中缺少partner_order_code，无法更新订单状态")
            return False

        # 根据订单号查询外卖订单
        order = order_dao.get_by_order_no(session, partner_order_code)

        if not order:
            logger.warning(f"未找到订单号为 {partner_order_code} 的订单")
            return False

        # 确认是外卖订单
        if not isinstance(order, DeliveryOrder):
            logger.warning(f"订单 {partner_order_code} 不是外卖订单，类型为: {type(order)}")
            return False

        # 更新蜂鸟订单号和状态
        order.fengniao_order_id = str(fengniao_order_id) if fengniao_order_id else order.fengniao_order_id
        order.fengniao_status = fengniao_status

        # 根据蜂鸟状态映射系统订单状态
        new_order_status = _map_fengniao_status_to_order_status(fengniao_status)

        if new_order_status:
            order.status = new_order_status
            logger.info(f"订单 {partner_order_code} 状态更新为: {new_order_status.value}")

        # 更新订单状态描述
        description = param.get("description", "")
        detail_description = param.get("detail_description", "")
        error_code = param.get("error_code", "")
        error_scene = param.get("error_scene", "")

        status_desc_parts = []
        if description:
            status_desc_parts.append(description)
        if detail_description:
            status_desc_parts.append(detail_description)
        if error_code:
            status_desc_parts.append(f"错误码: {error_code}")
        if error_scene:
            status_desc_parts.append(f"异常场景: {error_scene}")

        if status_desc_parts:
            order.current_status = " | ".join(status_desc_parts)

        session.commit()
        logger.info(f"外卖订单 {partner_order_code} 更新成功，蜂鸟订单号: {fengniao_order_id}, 蜂鸟状态: {fengniao_status}")

        return True

    except Exception as e:
        logger.error(f"更新外卖订单状态失败: {e}", exc_info=True)
        session.rollback()
        return False


@fengniao_router.post("/fengniao", summary="外部系统回调接口")
async def external_callback(request: Request, session: Session = Depends(get_db)):
    """蜂鸟配送回调接口

    处理蜂鸟配送的各种回调，包括：
    - 订单状态变更回调 (orderStatusNotify)
    - 逆向订单状态回调 (reverseOrderNotify)
    - 门店配送范围变更回调 (chainstoreServiceStatusNotify)
    - 门店状态变更回调 (chainstoreStatusNotify)
    - 门店营业时间变更回调 (chainstoreBusinessTimeNotify)
    - 异常报备回调 (abnormalReportNotify)
    - 商户出餐回调 (cookingFinishNotify)
    """
    try:
        # 获取回调数据
        body = await request.json()

        # 记录原始回调信息到日志
        logger.info(f"[{datetime.now()}] 蜂鸟回调信息: {json.dumps(body, ensure_ascii=False, indent=2)}")

        # 解析business_data
        business_data = _parse_business_data(body.get("business_data", "{}"))
        callback_type = business_data.get("callback_business_type", "unknown")

        logger.info(f"回调类型: {callback_type}")
        logger.info(f"应用ID: {body.get('app_id', 'N/A')}")
        logger.info(f"时间戳: {body.get('timestamp', 'N/A')}")

        # 保存回调数据到数据库
        callback_id = _save_callback_to_db(session, body)

        if callback_id:
            # 根据回调类型进行特殊处理
            if callback_type in ["orderStatusNotify", "reverseOrderNotify"]:
                # 订单状态变更回调，保存详细信息到专门的表
                logger.info("处理订单状态变更回调")
                _save_order_status_callback(session, callback_id, business_data)

                # 打印订单状态信息
                param = business_data.get("param", {})
                logger.info(f"订单号: {param.get('order_id', 'N/A')}")
                logger.info(f"外部订单号: {param.get('partner_order_code', 'N/A')}")
                logger.info(f"订单状态: {param.get('order_status', 'N/A')}")
                logger.info(f"骑手信息: {param.get('carrier_driver_name', 'N/A')} ({param.get('carrier_driver_phone', 'N/A')})")

                # 更新外卖订单状态
                logger.info("开始更新外卖订单状态")
                update_success = _update_delivery_order_status(session, business_data)
                if update_success:
                    logger.info("外卖订单状态更新成功")
                else:
                    logger.warning("外卖订单状态更新失败")

            elif callback_type == "chainstoreServiceStatusNotify":
                # 门店配送范围变更回调
                logger.info("处理门店配送范围变更回调")
                param = business_data.get("param", {})
                logger.info(f"门店编码: {param.get('out_shop_code', 'N/A')}")
                logger.info(f"回调场景: {param.get('option_type', 'N/A')}")

            elif callback_type == "chainstoreStatusNotify":
                # 门店状态变更回调
                logger.info("处理门店状态变更回调")
                param = business_data.get("param", {})
                logger.info(f"门店ID: {param.get('chain_store_id', 'N/A')}")
                logger.info(f"门店状态: {param.get('status', 'N/A')}")

            else:
                logger.info(f"处理其他类型回调: {callback_type}")

            # 提交数据库事务
            session.commit()
            logger.info("回调处理完成，数据已保存")
        else:
            logger.error("保存回调数据失败")
            session.rollback()

        # 返回成功响应
        return {
            "code": 200,
            "message": "success",
            "data": {
                "callback_type": callback_type,
                "processed_at": datetime.now().isoformat()
            }
        }

    except Exception as e:
        logger.error(f"处理蜂鸟回调失败: {e}", exc_info=True)
        session.rollback()

        # 根据文档，如果处理失败需要返回特定格式让蜂鸟重试
        return {
            "code": 500,
            "message": "处理失败",
            "_fengniao_code_": "500"  # 蜂鸟重试标识
        }
