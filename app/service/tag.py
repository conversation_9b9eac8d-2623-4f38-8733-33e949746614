from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.dao.tag import tag_dao
from app.dao.rule import rule_dao, rule_item_dao
from app.models.tag import Tag
from app.models.rule import Rule, RuleItem, RuleType, DiningReservationRule
from croniter import croniter


class TagService:
    """标签服务类"""

    @staticmethod
    def get_product_ids_by_tag_name(session: Session, tag_name: str) -> List[int]:
        """根据标签名称获取产品ID列表

        Args:
            session: 数据库会话
            tag_name: 标签名称

        Returns:
            List[int]: 产品ID列表
        """
        tag = tag_dao.get_by_name(session, tag_name)
        if not tag:
            return []

        return [product.id for product in tag.products]

    @staticmethod
    def get_rule_ids_by_tag_name(session: Session, tag_name: str) -> List[int]:
        """根据标签名称获取时间规则ID列表

        Args:
            session: 数据库会话
            tag_name: 标签名称

        Returns:
            List[int]: 规则ID列表
        """
        tag = tag_dao.get_by_name(session, tag_name)
        if not tag:
            return []

        return [rule.id for rule in tag.rules]

    @staticmethod
    def get_rules_with_items_by_tag_name(session: Session, tag_name: str) -> List[Dict[str, Any]]:
        """根据标签名称获取时间规则及其规则项

        Args:
            session: 数据库会话
            tag_name: 标签名称

        Returns:
            List[Dict[str, Any]]: 包含规则及规则项的字典列表
        """
        tag = tag_dao.get_by_name(session, tag_name)
        if not tag:
            return []

        result = []
        for rule in tag.rules:
            rule_items = rule_item_dao.get_by_rule(session, rule.id)
            rule_dict = {
                "id": rule.id,
                "name": rule.name,
                "status": rule.status,
                "type": rule.type.value if rule.type else None,
                "scope": rule.scope.value if rule.scope else None,
                "order_type": rule.order_type.value if rule.order_type else None,
                "created_at": rule.created_at.strftime("%Y-%m-%d %H:%M:%S") if rule.created_at else None,
                "updated_at": rule.updated_at.strftime("%Y-%m-%d %H:%M:%S") if rule.updated_at else None,
                "rule_items": []
            }

            # 如果是DINING_RESERVATION类型，获取dining_reservation_rules并生成时间
            if rule.type == RuleType.DINING_RESERVATION:
                dining_rule = rule_dao.get_dining_reservation_rule(session, rule.id)
                if dining_rule:
                    # 生成rule级别的时间
                    rule_times = TagService._generate_rule_times(dining_rule)
                    rule_dict["rule_times"] = rule_times

            for item in rule_items:
                item_dict = {
                    "id": item.id,
                    "rule_id": item.rule_id,
                    "name": item.name,
                    "alias": item.alias,
                    "start_time": item.start_time.strftime("%Y-%m-%d %H:%M:%S") if item.start_time else None,
                    "end_time": item.end_time.strftime("%Y-%m-%d %H:%M:%S") if item.end_time else None,
                    "start_time_cron_str": item.start_time_cron_str,
                    "end_time_cron_str": item.end_time_cron_str,
                    "order_deadline": item.order_deadline,
                    "cancellation_deadline": item.cancellation_deadline,
                    "generated_count": item.generated_count,
                    "quantity": item.quantity,
                    "order": item.order,
                    "allowed_operations": item.allowed_operations,
                    "forbidden_operations": item.forbidden_operations,
                    "meal_type": item.meal_type.value if item.meal_type else None,
                }

                # 如果是DINING_RESERVATION类型，生成rule_item级别的时间
                if rule.type == RuleType.DINING_RESERVATION:
                    rule_item_times = TagService._generate_rule_item_times(item)
                    item_dict["rule_items_times"] = rule_item_times

                rule_dict["rule_items"].append(item_dict)

            result.append(rule_dict)

        return result

    @staticmethod
    def generate_time_periods_by_tag(
        session: Session,
        tag_name: str,
        start_date: datetime,
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        """根据标签名称和时间规则生成日期时间段

        Args:
            session: 数据库会话
            tag_name: 标签名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            List[Dict[str, Any]]: 生成的时间段列表
        """
        tag = tag_dao.get_by_name(session, tag_name)
        if not tag:
            return []

        result = []

        for rule in tag.rules:
            rule_items = rule_item_dao.get_by_rule(session, rule.id)

            for item in rule_items:
                # 如果有cron表达式，使用cron生成时间段
                if item.start_time_cron_str and item.end_time_cron_str:
                    periods = TagService._generate_periods_from_cron(
                        item, start_date, end_date
                    )
                    result.extend(periods)
                # 如果有固定时间，使用固定时间
                elif item.start_time and item.end_time:
                    period = {
                        "rule_id": rule.id,
                        "rule_name": rule.name,
                        "rule_item_id": item.id,
                        "rule_item_name": item.name,
                        "rule_item_alias": item.alias,
                        "start_time": item.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                        "end_time": item.end_time.strftime("%Y-%m-%d %H:%M:%S"),
                        "order_deadline": item.order_deadline,
                        "cancellation_deadline": item.cancellation_deadline,
                        "quantity": item.quantity,
                        "meal_type": item.meal_type.value if item.meal_type else None,
                    }
                    result.append(period)

        return result

    @staticmethod
    def _generate_periods_from_cron(
        rule_item: RuleItem,
        start_date: datetime,
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        """根据cron表达式生成时间段

        Args:
            rule_item: 规则项
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            List[Dict[str, Any]]: 生成的时间段列表
        """
        periods = []

        try:
            # 创建cron迭代器
            start_cron = croniter(rule_item.start_time_cron_str, start_date)

            # 生成时间段
            current_date = start_date
            while current_date <= end_date:
                # 获取下一个开始时间
                next_start = start_cron.get_next(datetime)

                if next_start > end_date:
                    break

                # 计算结束时间
                end_cron = croniter(rule_item.end_time_cron_str, next_start)
                next_end = end_cron.get_next(datetime)

                period = {
                    "rule_id": rule_item.rule_id,
                    "rule_item_id": rule_item.id,
                    "rule_item_name": rule_item.name,
                    "rule_item_alias": rule_item.alias,
                    "start_time": next_start.strftime("%Y-%m-%d %H:%M:%S"),
                    "end_time": next_end.strftime("%Y-%m-%d %H:%M:%S"),
                    "order_deadline": rule_item.order_deadline,
                    "cancellation_deadline": rule_item.cancellation_deadline,
                    "quantity": rule_item.quantity,
                    "meal_type": rule_item.meal_type.value if rule_item.meal_type else None,
                }
                periods.append(period)

                current_date = next_start + timedelta(days=1)
                start_cron = croniter(rule_item.start_time_cron_str, current_date)

        except Exception as e:
            # 如果cron表达式解析失败，返回空列表
            print(f"Error parsing cron expression: {e}")
            return []

        return periods

    @staticmethod
    def _generate_rule_times(dining_rule: DiningReservationRule) -> List[Dict[str, str]]:
        """根据DiningReservationRule生成时间列表

        Args:
            dining_rule: 餐厅预订规则对象

        Returns:
            List[Dict[str, str]]: 生成的时间列表
        """
        rule_times = []

        # 如果没有cron表达式或generated_count，返回空列表
        if not dining_rule.dining_start_time_cron_str or not dining_rule.dining_end_time_cron_str:
            return rule_times

        if not dining_rule.verify_start_time_cron_str or not dining_rule.verify_end_time_cron_str:
            return rule_times

        generated_count = dining_rule.generated_count if dining_rule.generated_count else 5
        now = datetime.now()

        try:
            # 生成dining时间
            dining_start_times = TagService._get_next_times_from_cron(
                dining_rule.dining_start_time_cron_str, now, generated_count
            )
            dining_end_times = TagService._get_next_times_from_cron(
                dining_rule.dining_end_time_cron_str, now, generated_count
            )

            # 生成verify时间
            verify_start_times = TagService._get_next_times_from_cron(
                dining_rule.verify_start_time_cron_str, now, generated_count
            )
            verify_end_times = TagService._get_next_times_from_cron(
                dining_rule.verify_end_time_cron_str, now, generated_count
            )

            # 组合生成时间段
            for i in range(min(len(dining_start_times), len(dining_end_times))):
                dining_start = dining_start_times[i]
                dining_end = dining_end_times[i]

                # 计算order_deadline和cancel_deadline
                # 从dining_start_time中最早的时间减去对应的分钟数
                order_deadline_time = dining_start - timedelta(minutes=dining_rule.order_deadline if dining_rule.order_deadline else 0)
                cancel_deadline_time = dining_start - timedelta(minutes=dining_rule.cancellation_deadline if dining_rule.cancellation_deadline else 0)

                rule_times.append({
                    "dining_start_time": dining_start.strftime("%Y-%m-%d %H:%M"),
                    "dining_end_time": dining_end.strftime("%Y-%m-%d %H:%M"),
                    "verify_start_time": verify_start_times[i].strftime("%Y-%m-%d %H:%M") if i < len(verify_start_times) else None,
                    "verify_end_time": verify_end_times[i].strftime("%Y-%m-%d %H:%M") if i < len(verify_end_times) else None,
                    "order_deadline": order_deadline_time.strftime("%Y-%m-%d %H:%M"),
                    "cancel_deadline": cancel_deadline_time.strftime("%Y-%m-%d %H:%M"),
                })

        except Exception as e:
            print(f"Error generating rule times: {e}")

        return rule_times

    @staticmethod
    def _generate_rule_item_times(rule_item: RuleItem) -> List[Dict[str, str]]:
        """根据RuleItem生成时间列表

        Args:
            rule_item: 规则项对象

        Returns:
            List[Dict[str, str]]: 生成的时间列表
        """
        rule_item_times = []

        # 如果没有cron表达式或generated_count，返回空列表
        if not rule_item.start_time_cron_str or not rule_item.end_time_cron_str:
            return rule_item_times

        generated_count = rule_item.generated_count if rule_item.generated_count else 5
        now = datetime.now()

        try:
            # 生成开始和结束时间
            start_times = TagService._get_next_times_from_cron(
                rule_item.start_time_cron_str, now, generated_count
            )
            end_times = TagService._get_next_times_from_cron(
                rule_item.end_time_cron_str, now, generated_count
            )

            # 组合生成时间段
            for i in range(min(len(start_times), len(end_times))):
                start_time = start_times[i]
                end_time = end_times[i]

                # 计算order_deadline和cancel_deadline
                # 从start_time中最早的时间减去对应的分钟数
                order_deadline_time = start_time - timedelta(minutes=rule_item.order_deadline if rule_item.order_deadline else 0)
                cancel_deadline_time = start_time - timedelta(minutes=rule_item.cancellation_deadline if rule_item.cancellation_deadline else 0)

                rule_item_times.append({
                    "dining_start_time": start_time.strftime("%Y-%m-%d %H:%M"),
                    "dining_end_time": end_time.strftime("%Y-%m-%d %H:%M"),
                    "order_deadline": order_deadline_time.strftime("%Y-%m-%d %H:%M"),
                    "cancel_deadline": cancel_deadline_time.strftime("%Y-%m-%d %H:%M"),
                })

        except Exception as e:
            print(f"Error generating rule item times: {e}")

        return rule_item_times

    @staticmethod
    def _get_next_times_from_cron(cron_str: str, start_time: datetime, count: int) -> List[datetime]:
        """从cron表达式生成指定数量的时间

        Args:
            cron_str: cron表达式字符串
            start_time: 起始时间
            count: 生成数量

        Returns:
            List[datetime]: 生成的时间列表
        """
        times = []
        try:
            cron = croniter(cron_str, start_time)
            for _ in range(count):
                next_time = cron.get_next(datetime)
                times.append(next_time)
        except Exception as e:
            print(f"Error parsing cron expression '{cron_str}': {e}")

        return times


# 创建服务实例
tag_service = TagService()
