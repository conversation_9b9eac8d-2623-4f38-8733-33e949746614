import base64
import time
import logging
from hashlib import sha1
from typing import Dict, Any, Optional, List
from datetime import datetime

import requests
from Crypto.Cipher import PKCS1_v1_5 as PKCS1_v1_5_cipher
from Crypto.PublicKey import RSA
from sqlalchemy.orm import Session, joinedload

from app.core.config import settings
from app.models.order import DeliveryOrder, OrderType, OrderItem

# 获取logger
logger = logging.getLogger(__name__)

# 私钥
private_key_pem = '''***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'''


class FeieyunService():

    @staticmethod
    def _mask_name(name: str) -> str:
        """
        脱敏处理姓名

        Args:
            name: 原始姓名

        Returns:
            str: 脱敏后的姓名
        """
        if not name or len(name) == 0:
            return ""
        elif len(name) == 1:
            return name
        elif len(name) == 2:
            return name[0] + "*"
        else:
            # 保留第一个字符，其余用*代替
            return name[0] + "*" * (len(name) - 2) + name[-1]

    @staticmethod
    def _mask_phone(phone: str) -> str:
        """
        脱敏处理手机号

        Args:
            phone: 原始手机号

        Returns:
            str: 脱敏后的手机号
        """
        if not phone or len(phone) < 7:
            return "***"
        # 保留前3位和后4位，中间用*代替
        return phone[:3] + "****" + phone[-4:]

    @staticmethod
    def callback_decrypt(encrypted_content: str) -> str:
        # 加载私钥
        private_key = RSA.import_key(private_key_pem)
        cipher = PKCS1_v1_5_cipher.new(private_key)

        # Base64 解码
        decoded_content = base64.b64decode(encrypted_content)

        # RSA 解密
        sentinel = b'ERROR'
        try:
            decrypted_content = cipher.decrypt(decoded_content, sentinel)
            if decrypted_content == sentinel:
                # 解密失败，抛出异常
                raise Exception("RSA 解密失败，返回哨兵值")
            else:
                return decrypted_content.decode('utf-8')
        except Exception as e:
            # 捕获异常并重新抛出，可自定义异常类型
            raise Exception(f"解密失败: {e}")

    @staticmethod
    def callback_encrypt(content: str, public_key_path: str) -> str:
        """
        使用RSA公钥加密内容并返回Base64编码的字符串

        Args:
            content: 要加密的内容
            public_key_path: 公钥文件路径

        Returns:
            str: Base64编码的加密内容
        """
        try:
            # 读取公钥文件
            with open(public_key_path, "r") as f:
                public_key_data = f.read()

            # 加载公钥
            public_key = RSA.import_key(public_key_data)
            cipher = PKCS1_v1_5_cipher.new(public_key)

            # RSA加密
            encrypted_content = cipher.encrypt(content.encode('utf-8'))

            # Base64编码
            base64_encrypted = base64.b64encode(encrypted_content).decode('utf-8')

            return base64_encrypted
        except Exception as e:
            # 捕获异常并重新抛出
            raise Exception(f"加密失败: {e}")

    @staticmethod
    def _generate_signature(stime: str) -> str:
        """
        生成飞鹅云API签名

        Args:
            stime: 时间戳字符串

        Returns:
            str: SHA1签名（40位小写字符串）
        """
        s1 = sha1()
        signature_str = settings.FEIEYUN_USER + settings.FEIEYUN_UKEY + stime
        s1.update(signature_str.encode())
        return s1.hexdigest()

    @staticmethod
    def _get_str_width(text: str) -> int:
        """
        计算字符串的字节宽度（中文2字节，英文1字节）

        Args:
            text: 要计算的字符串

        Returns:
            int: 字节宽度
        """
        try:
            # 使用GBK编码计算字节数
            return len(text.encode('gbk'))
        except Exception:
            # 如果编码失败，使用UTF-8估算（中文3字节）
            return len(text.encode('utf-8'))

    @staticmethod
    def _format_product_line(name: str, price: float, quantity: int, amount: float,
                            name_width: int = 14, price_width: int = 6,
                            quantity_width: int = 3, amount_width: int = 6) -> str:
        """
        格式化商品行，处理长名称换行和对齐

        Args:
            name: 商品名称
            price: 单价
            quantity: 数量
            amount: 金额
            name_width: 名称列宽度（字节）
            price_width: 单价列宽度（字节）
            quantity_width: 数量列宽度（字节）
            amount_width: 金额列宽度（字节）

        Returns:
            str: 格式化后的商品行（可能包含多行）
        """
        price_str = f"{price:.1f}"
        quantity_str = str(quantity)
        amount_str = f"{amount:.1f}"

        # 填充单价、数量、金额到指定宽度
        price_str = price_str + ' ' * (price_width - len(price_str))
        quantity_str = quantity_str + ' ' * (quantity_width - len(quantity_str))
        amount_str = amount_str + ' ' * (amount_width - len(amount_str))

        result = ""
        name_bytes = name.encode('gbk')
        current_pos = 0

        # 处理名称换行
        while current_pos < len(name_bytes):
            # 取出当前行的字节
            line_bytes = name_bytes[current_pos:current_pos + name_width]
            try:
                line_text = line_bytes.decode('gbk')
            except UnicodeDecodeError:
                # 如果解码失败，可能是截断了中文字符，减少一个字节重试
                line_bytes = name_bytes[current_pos:current_pos + name_width - 1]
                try:
                    line_text = line_bytes.decode('gbk')
                except UnicodeDecodeError:
                    # 再次失败，使用原始文本
                    line_text = name[current_pos:current_pos + name_width // 2]
                    line_bytes = line_text.encode('gbk')

            current_pos += len(line_bytes)

            # 计算需要填充的空格数
            padding = name_width - len(line_bytes)
            line_text += ' ' * padding

            # 第一行添加价格、数量、金额
            if result == "":
                result = f"{line_text} {price_str} {quantity_str} {amount_str}<BR>"
            else:
                # 后续行只显示名称
                result += f"{line_text}<BR>"

        return result

    @staticmethod
    def _format_order_content(order: DeliveryOrder) -> str:
        """
        格式化订单打印内容

        Args:
            order: 外卖订单对象

        Returns:
            str: 格式化后的打印内容
        """
        content = ""

        # 标题
        content += "<CB>外卖订单</CB><BR>"
        content += "--------------------------------<BR>"

        # 订单基本信息
        content += f"订单号：{order.order_no}<BR>"
        if order.created_at:
            content += f"下单时间：{order.created_at.strftime('%Y-%m-%d %H:%M:%S')}<BR>"
        content += "--------------------------------<BR>"

        # 用户信息 - 使用 delivery_address_raw
        if order.delivery_address_raw:
            # 从 delivery_address_raw 中提取信息
            recipient_name = order.delivery_address_raw.get('name', '未知用户')
            recipient_phone = order.delivery_address_raw.get('phone', '未知手机号')

            # 脱敏处理
            masked_name = FeieyunService._mask_name(recipient_name)
            masked_phone = FeieyunService._mask_phone(recipient_phone)

            content += f"收货人：{masked_name}<BR>"
            content += f"联系电话：{masked_phone}<BR>"

            # 配送地址 - 不包含电话信息
            province = order.delivery_address_raw.get('province', '')
            city = order.delivery_address_raw.get('city', '')
            district = order.delivery_address_raw.get('district', '')
            detail = order.delivery_address_raw.get('detail', '')

            # 组合完整地址
            full_address = f"{province}{city}{district}{detail}"
            content += f"配送地址：{full_address}<BR>"
        elif order.user:
            # 如果没有 delivery_address_raw，回退到使用 user 信息
            user_name = order.user.real_name if hasattr(order.user, 'real_name') and order.user.real_name else "未知用户"
            user_phone = order.user.phone if hasattr(order.user, 'phone') else "未知手机号"

            # 脱敏处理
            masked_name = FeieyunService._mask_name(user_name)
            masked_phone = FeieyunService._mask_phone(user_phone)

            content += f"收货人：{masked_name}<BR>"
            content += f"联系电话：{masked_phone}<BR>"

            # 使用 delivery_address 字段
            if order.delivery_address:
                content += f"配送地址：{order.delivery_address}<BR>"

        # 配送时间
        if order.delivery_time:
            content += f"配送时间：{order.delivery_time.strftime('%Y-%m-%d %H:%M:%S')}<BR>"

        content += "--------------------------------<BR>"

        # 商品列表表头
        content += "名称           单价  数量 金额<BR>"
        content += "--------------------------------<BR>"

        # 商品明细
        items_total = 0.0
        if order.items:
            for item in order.items:
                product_name = item.product.name if item.product else "未知商品"
                price = item.price
                quantity = item.quantity
                subtotal = item.payable_amount

                # 格式化商品行
                product_line = FeieyunService._format_product_line(
                    product_name, price, quantity, subtotal
                )
                content += product_line
                items_total += subtotal

        content += "--------------------------------<BR>"

        # 金额汇总
        content += f"商品小计：{items_total:.1f}元<BR>"
        if order.delivery_fee:
            content += f"配送费：{order.delivery_fee:.1f}元<BR>"
        if order.discount_amount and order.discount_amount > 0:
            content += f"优惠金额：{order.discount_amount:.1f}元<BR>"
        content += f"应付金额：{order.payable_amount:.1f}元<BR>"
        if order.actual_amount_paid:
            content += f"实付金额：{order.actual_amount_paid:.1f}元<BR>"

        content += "<BR>"

        # 支付和状态信息
        if order.payment_method:
            payment_method_map = {
                "wechat_pay": "微信支付",
                "alipay": "支付宝",
                "cash": "现金",
                "account_balance": "账户余额",
                "enterprise_account_balance": "企业账户",
                "bank_transfer": "银行转账"
            }
            payment_method_text = payment_method_map.get(order.payment_method.value, order.payment_method.value)
            content += f"支付方式：{payment_method_text}<BR>"

        if order.status:
            status_map = {
                "pending": "待处理",
                "paid": "已支付",
                "shipped": "已发货",
                "delivered": "已送达",
                "completed": "已完成",
                "cancelled": "已取消",
                "refunded": "已退款",
                "prepared": "已备餐"
            }
            status_text = status_map.get(order.status.value, order.status.value)
            content += f"订单状态：{status_text}<BR>"

        if order.payment_time:
            content += f"支付时间：{order.payment_time.strftime('%Y-%m-%d %H:%M:%S')}<BR>"

        content += "<BR>"

        # 生成二维码 - DELIVERY: + 订单号
        qr_content = f"DELIVERY:{order.order_no}"
        content += f"<QR>{qr_content}</QR><BR>"

        # 切刀指令
        content += "<CUT>"

        return content

    @staticmethod
    def _call_print_api(sn: str, content: str, times: int = 1) -> Dict[str, Any]:
        """
        调用飞鹅云打印API

        Args:
            sn: 打印机编号
            content: 打印内容
            times: 打印次数

        Returns:
            Dict[str, Any]: API响应结果
        """
        try:
            # 检查配置
            if not settings.FEIEYUN_USER or not settings.FEIEYUN_UKEY:
                logger.error("飞鹅云配置不完整，请检查 FEIEYUN_USER 和 FEIEYUN_UKEY")
                return {
                    "success": False,
                    "message": "飞鹅云配置不完整"
                }

            # 检查内容长度（不能超过5000字节）
            content_bytes = content.encode('utf-8')
            if len(content_bytes) > 5000:
                logger.warning(f"打印内容超过5000字节限制: {len(content_bytes)} bytes")
                return {
                    "success": False,
                    "message": f"打印内容超过5000字节限制: {len(content_bytes)} bytes"
                }

            # 生成时间戳和签名
            stime = str(int(time.time()))
            sig = FeieyunService._generate_signature(stime)

            # 构建请求参数
            params = {
                'user': settings.FEIEYUN_USER,
                'sig': sig,
                'stime': stime,
                'apiname': 'Open_printMsg',
                'sn': sn,
                'content': content,
                'times': str(times)
            }

            # 发送请求
            logger.info(f"发送打印请求到飞鹅云，SN: {sn}, 内容长度: {len(content_bytes)} bytes")
            response = requests.post(settings.FEIEYUN_API_URL, data=params, timeout=30)

            # 检查响应状态码
            if response.status_code != 200:
                logger.error(f"飞鹅云API请求失败，状态码: {response.status_code}")
                return {
                    "success": False,
                    "message": f"API请求失败，状态码: {response.status_code}"
                }

            # 解析响应
            result = response.json()
            logger.info(f"飞鹅云API响应: {result}")

            if result.get('ret') == 0:
                return {
                    "success": True,
                    "message": "打印成功",
                    "order_id": result.get('data'),
                    "response": result
                }
            else:
                return {
                    "success": False,
                    "message": result.get('msg', '打印失败'),
                    "response": result
                }

        except requests.exceptions.Timeout:
            logger.error("飞鹅云API请求超时")
            return {
                "success": False,
                "message": "API请求超时"
            }
        except Exception as e:
            logger.error(f"调用飞鹅云API失败: {str(e)}")
            return {
                "success": False,
                "message": f"调用API失败: {str(e)}"
            }

    @staticmethod
    def print_delivery_orders(
        db: Session,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        order_id: Optional[int] = None,
        order_ids: Optional[List[int]] = None,
        sn: Optional[str] = None,
        times: int = 1
    ) -> Dict[str, Any]:
        """
        打印快递订单

        Args:
            db: 数据库会话
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            order_id: 单个订单ID
            order_ids: 订单ID列表
            sn: 打印机编号（可选，默认使用配置中的SN）
            times: 打印次数，默认1次

        Returns:
            Dict[str, Any]: {
                "success": bool,
                "message": str,
                "printed_count": int,
                "print_results": List[Dict]
            }
        """
        try:
            # 使用配置中的SN或传入的SN
            printer_sn = sn or settings.FEIEYUN_SN
            if not printer_sn:
                logger.error("未配置打印机编号")
                return {
                    "success": False,
                    "message": "未配置打印机编号",
                    "printed_count": 0,
                    "print_results": []
                }

            # 构建查询
            query = db.query(DeliveryOrder).options(
                joinedload(DeliveryOrder.user),
                joinedload(DeliveryOrder.items).joinedload(OrderItem.product)
            )

            # 根据不同的查询条件过滤
            if order_id:
                # 单个订单ID查询
                query = query.filter(DeliveryOrder.id == order_id)
                logger.info(f"按订单ID查询: {order_id}")
            elif order_ids:
                # 订单ID列表查询
                query = query.filter(DeliveryOrder.id.in_(order_ids))
                logger.info(f"按订单ID列表查询: {order_ids}")
            elif start_date or end_date:
                # 日期范围查询
                if start_date:
                    try:
                        start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
                        query = query.filter(DeliveryOrder.created_at >= start_datetime)
                        logger.info(f"添加开始日期过滤: {start_datetime}")
                    except ValueError:
                        logger.error(f"开始日期格式错误: {start_date}")
                        return {
                            "success": False,
                            "message": f"无效的开始日期格式: {start_date}，请使用 YYYY-MM-DD",
                            "printed_count": 0,
                            "print_results": []
                        }

                if end_date:
                    try:
                        end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
                        # 设置为当天的23:59:59
                        end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                        query = query.filter(DeliveryOrder.created_at <= end_datetime)
                        logger.info(f"添加结束日期过滤: {end_datetime}")
                    except ValueError:
                        logger.error(f"结束日期格式错误: {end_date}")
                        return {
                            "success": False,
                            "message": f"无效的结束日期格式: {end_date}，请使用 YYYY-MM-DD",
                            "printed_count": 0,
                            "print_results": []
                        }
            else:
                logger.error("必须提供查询条件（order_id、order_ids 或 日期范围）")
                return {
                    "success": False,
                    "message": "必须提供查询条件（order_id、order_ids 或 日期范围）",
                    "printed_count": 0,
                    "print_results": []
                }

            # 执行查询
            orders = query.order_by(DeliveryOrder.created_at.desc()).all()
            logger.info(f"查询到 {len(orders)} 个外卖订单")

            if not orders:
                return {
                    "success": True,
                    "message": "未找到符合条件的订单",
                    "printed_count": 0,
                    "print_results": []
                }

            # 打印每个订单
            print_results = []
            success_count = 0

            for order in orders:
                try:
                    # 格式化订单内容
                    content = FeieyunService._format_order_content(order)

                    # 调用打印API
                    result = FeieyunService._call_print_api(printer_sn, content, times)

                    if result.get('success'):
                        success_count += 1

                    print_results.append({
                        "order_id": order.id,
                        "order_no": order.order_no,
                        "success": result.get('success'),
                        "message": result.get('message'),
                        "print_order_id": result.get('order_id')
                    })

                except Exception as e:
                    logger.error(f"打印订单 {order.id} 失败: {str(e)}")
                    print_results.append({
                        "order_id": order.id,
                        "order_no": order.order_no,
                        "success": False,
                        "message": f"打印失败: {str(e)}"
                    })

            return {
                "success": success_count > 0,
                "message": f"成功打印 {success_count}/{len(orders)} 个订单",
                "printed_count": success_count,
                "total_count": len(orders),
                "print_results": print_results
            }

        except Exception as e:
            logger.error(f"打印外卖订单失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return {
                "success": False,
                "message": f"打印失败: {str(e)}",
                "printed_count": 0,
                "print_results": []
            }


feieyun_service = FeieyunService()

if __name__ == "__main__":
    # 测试用的加密内容
    test_encrypted_content = "rOc9B1X0JUsXC4Q9Lda42op3vDKka8WaMWWcnZSC5ARp2/7bshnUlvWguizy5W3jkXP+Et+ywyg+YKfCgm5qmqZaA5ODNz5qMiOLynf/QNpbmpvrSb4F/hRGsbCqfgjRCztpJDl27w/KqmGGjzMmZFyZB5MeO9inrmN/0bYejH4="

    try:
        result = feieyun_service.callback_decrypt(test_encrypted_content)
        print(f"解密结果: {result}")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
