from typing import List, Optional

from pydantic import BaseModel, ConfigDict


# Tag schemas
class TagBase(BaseModel):
    name: str


class TagCreate(TagBase):
    pass


class TagUpdate(TagBase):
    pass


class TagResponse(TagBase):
    id: int


class TagInDB(TagResponse):
    model_config = ConfigDict(from_attributes=True)


class Tag(TagInDB):
    pass


class MultiProductBinding(BaseModel):
    product_ids: List[int]


class MultiRuleBinding(BaseModel):
    """标签与规则批量绑定"""
    rule_ids: List[int]


class TagWithRules(TagResponse):
    """带规则的标签响应"""
    rule_ids: List[int] = []

    model_config = ConfigDict(from_attributes=True)
