"""蜂鸟配送回调相关DAO"""
import logging
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session

from app.dao.base import DAO
from app.models.fengniao import FengniaoCallback, FengniaoCallbackOrderStatus

logger = logging.getLogger(__name__)


class FengniaoCallbackDAO(DAO):
    """蜂鸟回调DAO"""
    
    def __init__(self):
        super().__init__(FengniaoCallback)
    
    def create_callback(self, session: Session, callback_data: Dict[str, Any]) -> FengniaoCallback:
        """创建回调记录
        
        Args:
            session: 数据库会话
            callback_data: 回调数据
            
        Returns:
            创建的回调记录
        """
        callback = FengniaoCallback(**callback_data)
        session.add(callback)
        session.flush()  # 获取ID
        return callback
    
    def get_by_app_id_and_timestamp(self, session: Session, app_id: str, timestamp: int) -> Optional[FengniaoCallback]:
        """根据app_id和timestamp查询回调记录
        
        Args:
            session: 数据库会话
            app_id: 应用ID
            timestamp: 时间戳
            
        Returns:
            回调记录或None
        """
        return session.query(self.model).filter(
            self.model.app_id == app_id,
            self.model.timestamp == timestamp
        ).first()
    
    def get_by_callback_type(self, session: Session, callback_type: str, limit: int = 100) -> List[FengniaoCallback]:
        """根据回调类型查询回调记录
        
        Args:
            session: 数据库会话
            callback_type: 回调类型
            limit: 限制数量
            
        Returns:
            回调记录列表
        """
        return session.query(self.model).filter(
            self.model.callback_type == callback_type
        ).order_by(self.model.created_at.desc()).limit(limit).all()


class FengniaoCallbackOrderStatusDAO(DAO):
    """蜂鸟订单状态回调DAO"""
    
    def __init__(self):
        super().__init__(FengniaoCallbackOrderStatus)
    
    def create_order_status_callback(self, session: Session, callback_id: int, order_data: Dict[str, Any]) -> FengniaoCallbackOrderStatus:
        """创建订单状态回调记录
        
        Args:
            session: 数据库会话
            callback_id: 回调记录ID
            order_data: 订单数据
            
        Returns:
            创建的订单状态回调记录
        """
        order_status_callback = FengniaoCallbackOrderStatus(
            callback_id=callback_id,
            **order_data
        )
        session.add(order_status_callback)
        session.flush()
        return order_status_callback
    
    def get_by_partner_order_code(self, session: Session, partner_order_code: str) -> List[FengniaoCallbackOrderStatus]:
        """根据外部订单号查询订单状态回调记录
        
        Args:
            session: 数据库会话
            partner_order_code: 外部订单号
            
        Returns:
            订单状态回调记录列表
        """
        return session.query(self.model).filter(
            self.model.partner_order_code == partner_order_code
        ).order_by(self.model.created_at.desc()).all()
    
    def get_by_fengniao_order_id(self, session: Session, order_id: int) -> List[FengniaoCallbackOrderStatus]:
        """根据蜂鸟订单号查询订单状态回调记录

        Args:
            session: 数据库会话
            order_id: 蜂鸟订单号

        Returns:
            订单状态回调记录列表
        """
        return session.query(self.model).filter(
            self.model.order_id == order_id
        ).order_by(self.model.created_at.desc()).all()

    def update_order_status_callback(self, session: Session, record_id: int, order_data: Dict[str, Any]) -> Optional[FengniaoCallbackOrderStatus]:
        """更新订单状态回调记录

        Args:
            session: 数据库会话
            record_id: 记录ID
            order_data: 订单数据

        Returns:
            更新后的订单状态回调记录，如果记录不存在返回None
        """
        record = session.query(self.model).filter(self.model.id == record_id).first()
        if record:
            for key, value in order_data.items():
                if hasattr(record, key):
                    setattr(record, key, value)
            session.flush()
            return record
        return None


# 创建DAO实例
fengniao_callback_dao = FengniaoCallbackDAO()
fengniao_callback_order_status_dao = FengniaoCallbackOrderStatusDAO()
