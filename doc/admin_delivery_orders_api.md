# 管理员外卖订单查询API文档

## 接口概述

该接口提供给小程序管理员查询外卖类型订单的功能，支持根据日期范围、订单状态、用户信息等条件进行查询。

## 接口信息

- **URL**: `/api/v1/wx/admin/delivery/delivery_orders`
- **方法**: `GET`
- **权限**: 需要管理员权限（miniapp:manage）

## 请求参数

### Headers
- `token` (string, optional): 用户认证token

### Query Parameters
- `start_date` (string, optional): 开始日期，格式：YYYY-MM-DD
- `end_date` (string, optional): 结束日期，格式：YYYY-MM-DD
- `order_status` (string, optional): 订单状态
  - 可选值：`pending`, `paid`, `cancelled`, `completed`, `shipped`, `delivered`, `verified`, `refunded`
- `payment_status` (string, optional): 支付状态
  - 可选值：`unpaid`, `paid`, `partial_paid`, `refunded`
- `user_phone` (string, optional): 用户手机号（支持模糊查询）
- `order_no` (string, optional): 订单号（支持模糊查询）
- `page` (integer, optional): 页码，默认为1
- `page_size` (integer, optional): 每页记录数，默认为10

## 响应格式

### 成功响应 (200)
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 123,
                "order_no": "O20240115120000123456",
                "status": "completed",
                "payment_status": "paid",
                "payment_method": "wechat_pay",
                "total_amount": 68.50,
                "payable_amount": 68.50,
                "actual_amount_paid": 68.50,
                "discount_amount": 0.0,
                "created_at": "2024-01-15 12:00:00",
                "updated_at": "2024-01-15 12:30:00",
                "payment_time": "2024-01-15 12:05:00",
                "user_id": 456,
                "user_name": "张三",
                "user_phone": "13800138000",
                "user_nick_name": "美食爱好者",
                "delivery_address": "北京市朝阳区某某街道123号",
                "delivery_address_raw": {
                    "name": "张三",
                    "phone": "13800138000",
                    "province": "北京市",
                    "city": "北京市",
                    "district": "朝阳区",
                    "detail": "某某街道123号",
                    "latitude": 39.9042,
                    "longitude": 116.4074
                },
                "delivery_time": "2024-01-15 12:30:00",
                "delivery_time_raw": {
                    "type": "immediate",
                    "value": "立即配送"
                },
                "delivery_fee": 8.50,
                "fengniao_order_id": "FN20240115001",
                "fengniao_status": 4,
                "items": [
                    {
                        "id": 789,
                        "product_id": 101,
                        "product_name": "宫保鸡丁",
                        "quantity": 1,
                        "price": 28.00,
                        "subtotal": 28.00,
                        "final_price": 28.00,
                        "payable_amount": 28.00,
                        "pricing_remark": ""
                    }
                ]
            }
        ],
        "total": 25,
        "page": 1,
        "page_size": 10,
        "total_pages": 3
    }
}
```

### 错误响应

#### 权限不足 (403)
```json
{
    "code": 403,
    "message": "无权限访问",
    "data": null
}
```

#### 参数错误 (400)
```json
{
    "code": 400,
    "message": "无效的开始日期格式，请使用 YYYY-MM-DD",
    "data": null
}
```

#### 服务器错误 (500)
```json
{
    "code": 500,
    "message": "查询失败: 数据库连接错误",
    "data": null
}
```

## 使用示例

### 查询所有外卖订单
```bash
GET /api/v1/wx/admin/delivery/delivery_orders
Headers: token: your_admin_token
```

### 按日期范围查询
```bash
GET /api/v1/wx/admin/delivery/delivery_orders?start_date=2024-01-01&end_date=2024-01-31
Headers: token: your_admin_token
```

### 按订单状态查询
```bash
GET /api/v1/wx/admin/delivery/delivery_orders?order_status=completed&page=1&page_size=20
Headers: token: your_admin_token
```

### 按用户手机号查询
```bash
GET /api/v1/wx/admin/delivery/delivery_orders?user_phone=138
Headers: token: your_admin_token
```

## 返回字段说明

### 订单基本信息
- `id`: 订单ID
- `order_no`: 订单号
- `status`: 订单状态
- `payment_status`: 支付状态
- `payment_method`: 支付方式
- `total_amount`: 订单总金额
- `payable_amount`: 应付金额
- `actual_amount_paid`: 实际支付金额
- `discount_amount`: 优惠金额
- `created_at`: 创建时间
- `updated_at`: 更新时间
- `payment_time`: 支付时间

### 用户信息
- `user_id`: 用户ID
- `user_name`: 用户真实姓名
- `user_phone`: 用户手机号
- `user_nick_name`: 用户微信昵称

### 配送信息
- `delivery_address`: 配送地址（格式化字符串）
- `delivery_address_raw`: 配送地址原始数据（JSON对象）
- `delivery_time`: 配送时间
- `delivery_time_raw`: 配送时间原始数据（JSON对象）
- `delivery_fee`: 配送费
- `fengniao_order_id`: 蜂鸟订单号
- `fengniao_status`: 蜂鸟订单状态

### 订单项信息
- `items`: 订单项列表
  - `id`: 订单项ID
  - `product_id`: 商品ID
  - `product_name`: 商品名称
  - `quantity`: 数量
  - `price`: 单价
  - `subtotal`: 小计
  - `final_price`: 最终单价
  - `payable_amount`: 应付金额
  - `pricing_remark`: 计价说明

### 分页信息
- `total`: 总记录数
- `page`: 当前页码
- `page_size`: 每页记录数
- `total_pages`: 总页数

## 注意事项

1. 该接口需要管理员权限，请确保token对应的用户具有`miniapp:manage`权限
2. 日期格式必须为`YYYY-MM-DD`，如：`2024-01-15`
3. 日期查询基于订单创建时间（`created_at`字段）
4. 手机号和订单号支持模糊查询
5. 默认按订单创建时间倒序排列
6. 分页从第1页开始
