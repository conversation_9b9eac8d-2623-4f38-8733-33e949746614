# 外卖订单取消费用处理实现说明

## 概述
在订单取消服务中，针对外卖订单（type == delivery）增加了蜂鸟订单预取消和正式取消的处理逻辑，确保在取消外卖订单时：
1. 先调用蜂鸟预取消接口检查订单是否可以取消
2. 获取取消费用
3. 在退款时扣除取消费用
4. 退款成功后调用蜂鸟正式取消接口

## 修改的文件
- `app/service/order_cancel_service.py`

## 实现细节

### 1. 在 `_cancel_paid_order_logic` 方法中增加外卖订单检查

在处理 paid 订单取消时，首先检查订单类型是否为外卖订单（`OrderType.DELIVERY`）：

```python
# 1. 如果是外卖订单，先调用蜂鸟预取消接口检查
cancel_cost_cent = 0  # 取消费用（分）
if order.type == OrderType.DELIVERY:
    logger.info(f"[订单取消服务] 检测到外卖订单，调用蜂鸟预取消接口")
    
    # 检查是否有蜂鸟订单号
    if not hasattr(order, 'fengniao_order_id') or not order.fengniao_order_id:
        logger.error(f"[订单取消服务] 外卖订单缺少蜂鸟订单号")
        return {
            "message": "外卖订单缺少蜂鸟订单号，无法取消",
            "status": 400
        }
    
    # 调用蜂鸟预取消接口
    from app.service.fengniao import FengniaoClient
    fengniao_client = FengniaoClient()
    
    # 获取取消原因列表（使用默认的商户取消原因）
    cancel_reason_code = 32  # 默认取消原因code
    
    # 调用预取消接口
    pre_cancel_data = {
        "order_id": order.fengniao_order_id,
        "order_cancel_code": cancel_reason_code
    }
    
    pre_cancel_result = fengniao_client.pre_cancel_order(pre_cancel_data)
    
    # 检查接口调用是否成功
    if pre_cancel_result.get('code') != '200':
        # 返回错误信息
        return {
            "message": f"外卖订单无法取消: {friendly_msg}",
            "status": 400
        }
    
    # 解析业务数据，获取取消费用
    business_data = pre_cancel_result.get('business_data')
    if business_data:
        cancel_cost_cent = business_data.get('actual_cancel_cost_cent', 0)
        logger.info(f"[订单取消服务] 蜂鸟预取消费用: {cancel_cost_cent} 分")
```

### 2. 将取消费用传递给单项取消逻辑

修改 `_cancel_single_item_logic` 方法签名，增加 `cancel_cost_cent` 参数：

```python
@staticmethod
async def _cancel_single_item_logic(
    db: Session,
    order,
    order_item_id: int,
    user_id: int,
    cancel_cost_cent: int = 0  # 新增参数
) -> Dict[str, Any]:
```

在调用 `_cancel_single_item_logic` 时传入取消费用：

```python
result = await OrderCancelService._cancel_single_item_logic(
    db, order, first_item.id, user_id, cancel_cost_cent=cancel_cost_cent
)
```

### 3. 在退款金额计算中扣除取消费用

在 `_cancel_single_item_logic` 方法中，计算退款金额后扣除取消费用：

```python
# 4. 计算退款金额（已扣除优惠金额）
refund_amount, coupon_refund_info = await OrderCancelService._calculate_refund_amount(
    db, order, order_item, is_business_dining
)
logger.info(f"[订单取消服务] 原始退款金额: {refund_amount}")

# 4.1 如果有取消费用，从退款金额中扣除
if cancel_cost_cent > 0:
    cancel_cost_yuan = cancel_cost_cent / 100  # 转换为元
    logger.info(f"[订单取消服务] 外卖订单取消费用: {cancel_cost_yuan} 元，将从退款金额中扣除")
    
    # 扣除取消费用
    refund_amount = max(0, refund_amount - cancel_cost_yuan)
    logger.info(f"[订单取消服务] 扣除取消费用后的退款金额: {refund_amount}")
    
    if refund_amount == 0:
        logger.warning(f"[订单取消服务] 取消费用等于或超过退款金额，实际退款金额为0")
```

### 4. 退款成功后调用蜂鸟正式取消接口

在退款成功后，调用蜂鸟正式取消接口：

```python
# 6.4 如果是外卖订单，调用蜂鸟正式取消接口
if order.type == OrderType.DELIVERY and hasattr(order, 'fengniao_order_id') and order.fengniao_order_id:
    logger.info(f"[订单取消服务] 调用蜂鸟正式取消接口")
    
    from app.service.fengniao import FengniaoClient
    fengniao_client = FengniaoClient()
    
    # 调用正式取消接口
    cancel_data = {
        "order_id": order.fengniao_order_id,
        "order_cancel_code": 32,  # 商户取消
        "order_cancel_role": 1,  # 1商户取消
        "order_cancel_other_reason": "用户申请退款",
        "actual_cancel_cost_cent": cancel_cost_cent  # 传入预取消接口返回的取消费用
    }
    
    cancel_result = fengniao_client.cancel_order(cancel_data)
    
    # 检查接口调用是否成功
    if cancel_result.get('code') != '200':
        # 注意：这里不抛出异常，因为退款已经成功，只记录错误
        logger.warning(f"[订单取消服务] 退款已成功，但蜂鸟订单取消失败，需要人工处理")
    else:
        logger.info(f"[订单取消服务] 蜂鸟订单取消成功")
```

## 错误处理

### 预取消接口错误处理
- **B0108**: 运单状态不允许取消 - 返回友好错误信息给用户
- **B0110**: 运单已取消 - 返回友好错误信息给用户
- **B0111**: 运单不存在 - 返回友好错误信息给用户

### 正式取消接口错误处理
- 如果正式取消接口调用失败，**不会回滚退款**，因为退款已经成功
- 只记录错误日志，提示需要人工处理
- 这样设计是为了保护用户利益，避免退款成功但订单状态不一致的情况

## API 返回格式

### 成功取消（无取消费用）
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "message": "Paid订单取消成功（含优惠券回退）",
        "status": 200,
        "coupon_rollback": {
            "success": true,
            "message": "优惠券回退成功，共回退 1 条记录",
            "rollback_count": 1
        }
    }
}
```

### 成功取消（有取消费用）
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "message": "Paid订单取消成功（含优惠券回退），扣除取消费用 5.00 元",
        "status": 200,
        "cancel_cost": 5.00,
        "coupon_rollback": {
            "success": true,
            "message": "优惠券回退成功，共回退 1 条记录",
            "rollback_count": 1
        }
    }
}
```

### 取消失败（订单不可取消）
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "message": "外卖订单无法取消: 运单状态不允许取消",
        "status": 400
    }
}
```

## 测试建议

### 测试场景
1. **正常取消（无取消费用）**
   - 创建外卖订单并支付
   - 在允许取消的时间内取消订单
   - 验证退款金额正确
   - 验证蜂鸟订单已取消

2. **正常取消（有取消费用）**
   - 创建外卖订单并支付
   - 在产生取消费用的时间段取消订单
   - 验证退款金额 = 订单金额 - 取消费用
   - 验证蜂鸟订单已取消

3. **取消失败（订单不可取消）**
   - 创建外卖订单并支付
   - 等待订单状态变为不可取消（如骑手已取货）
   - 尝试取消订单
   - 验证返回错误信息

4. **边界情况（取消费用等于订单金额）**
   - 创建外卖订单并支付
   - 取消费用等于或超过订单金额
   - 验证退款金额为 0
   - 验证蜂鸟订单已取消

## 注意事项

1. **取消费用单位**: 蜂鸟接口返回的取消费用单位是**分**，需要转换为**元**后再进行计算
2. **退款顺序**: 先扣除取消费用，再进行退款，确保退款金额正确
3. **事务处理**: 退款和订单状态更新在同一个事务中，确保数据一致性
4. **错误处理**: 正式取消接口失败不影响退款，但需要记录日志供人工处理
5. **日志记录**: 所有关键步骤都有详细的日志记录，便于问题排查

