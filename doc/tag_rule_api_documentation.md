# 标签与时间规则关联 API 文档

## 概述

本文档描述了标签与时间规则关联功能的API接口，包括：
1. 标签与规则的绑定/解绑
2. 根据标签名称获取产品ID和规则ID
3. 根据标签名称获取时间规则及规则项
4. 根据标签和时间规则生成日期时间段
5. 外卖订单关联时间规则

## API 接口列表

### 1. 绑定标签到规则

**接口地址**: `POST /api/v1/tags/{tag_id}/bind/rules`

**请求参数**:
```json
{
  "rule_ids": [1, 2, 3]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "午餐标签"
  }
}
```

---

### 2. 解绑标签与规则

**接口地址**: `POST /api/v1/tags/{tag_id}/unbind/rules`

**请求参数**:
```json
{
  "rule_ids": [1, 2]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "午餐标签"
  }
}
```

---

### 3. 根据标签名称获取产品ID列表

**接口地址**: `GET /api/v1/tags/by-name/{tag_name}/products`

**请求示例**: `GET /api/v1/tags/by-name/午餐/products`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tag_name": "午餐",
    "product_ids": [1, 2, 3, 5, 8]
  }
}
```

---

### 4. 根据标签名称获取规则ID列表

**接口地址**: `GET /api/v1/tags/by-name/{tag_name}/rules`

**请求示例**: `GET /api/v1/tags/by-name/午餐/rules`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tag_name": "午餐",
    "rule_ids": [1, 2]
  }
}
```

---

### 5. 根据标签名称获取时间规则及规则项

**接口地址**: `GET /api/v1/tags/by-name/{tag_name}/rules/with-items`

**请求示例**: `GET /api/v1/tags/by-name/午餐/rules/with-items`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tag_name": "午餐",
    "rules": [
      {
        "id": 1,
        "name": "工作日午餐规则",
        "status": "active",
        "type": "dining_reservation",
        "scope": "order",
        "order_type": "buffet",
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 10:00:00",
        "rule_items": [
          {
            "id": 1,
            "rule_id": 1,
            "name": "工作日午餐时段",
            "alias": "weekday_lunch",
            "start_time": null,
            "end_time": null,
            "start_time_cron_str": "0 11 30 * * 1-5",
            "end_time_cron_str": "0 13 30 * * 1-5",
            "order_deadline": 60,
            "cancellation_deadline": 30,
            "generated_count": 1,
            "quantity": 100,
            "order": 1,
            "allowed_operations": "",
            "forbidden_operations": "",
            "meal_type": "lunch"
          }
        ]
      }
    ]
  }
}
```

---

### 6. 根据标签生成时间段

**接口地址**: `POST /api/v1/tags/by-name/{tag_name}/time-periods`

**请求参数**:
- `start_date`: 开始日期，格式：YYYY-MM-DD
- `end_date`: 结束日期，格式：YYYY-MM-DD

**请求示例**: 
```
POST /api/v1/tags/by-name/午餐/time-periods?start_date=2024-01-01&end_date=2024-01-07
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tag_name": "午餐",
    "start_date": "2024-01-01",
    "end_date": "2024-01-07",
    "periods": [
      {
        "rule_id": 1,
        "rule_item_id": 1,
        "rule_item_name": "工作日午餐时段",
        "rule_item_alias": "weekday_lunch",
        "start_time": "2024-01-01 11:30:00",
        "end_time": "2024-01-01 13:30:00",
        "order_deadline": 60,
        "cancellation_deadline": 30,
        "quantity": 100,
        "meal_type": "lunch"
      },
      {
        "rule_id": 1,
        "rule_item_id": 1,
        "rule_item_name": "工作日午餐时段",
        "rule_item_alias": "weekday_lunch",
        "start_time": "2024-01-02 11:30:00",
        "end_time": "2024-01-02 13:30:00",
        "order_deadline": 60,
        "cancellation_deadline": 30,
        "quantity": 100,
        "meal_type": "lunch"
      }
    ]
  }
}
```

---

### 7. 创建外卖订单（关联时间规则）

**接口地址**: `POST /api/v1/wechat-mini-app/orders/delivery`

**请求参数**:
```json
{
  "order_type": "delivery",
  "items": [
    {
      "dish_id": 1,
      "name": "宫保鸡丁",
      "price": 28.00,
      "quantity": 1
    }
  ],
  "total_amount": 28.00,
  "delivery_address": {
    "name": "张三",
    "phone": "13800138000",
    "detail": "北京市朝阳区建国路88号",
    "latitude": 39.9042,
    "longitude": 116.4074
  },
  "delivery_time": {
    "value": "12:00-13:00",
    "label": "中午12:00-13:00"
  },
  "delivery_fee": 8.50,
  "service_goods_id": 3008,
  "base_goods_id": 30024,
  "t_index_id": "b69d90c2-b7f0-4f0b-a62c-4ad64aa50835",
  "rule_id": 1,
  "rule_item_id": 1
}
```

**字段说明**:
- `rule_id`: 关联的时间规则ID（可选）
- `rule_item_id`: 关联的时间规则项ID（可选）

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "order_id": 12345,
    "order_no": "ORD20240115120000001",
    "status": "pending",
    "payment_status": "unpaid",
    "total_amount": 36.50,
    "payable_amount": 36.50,
    "delivery_fee": 8.50,
    "rule_id": 1,
    "rule_item_id": 1
  }
}
```

---

## 使用场景示例

### 场景1：设置午餐时段的产品和规则

1. 创建标签"午餐"
2. 创建时间规则"工作日午餐规则"，包含规则项"11:30-13:30"
3. 将标签绑定到产品：`POST /api/v1/tags/1/bind/products`
4. 将标签绑定到规则：`POST /api/v1/tags/1/bind/rules`

### 场景2：获取午餐时段的可用产品

1. 根据标签获取产品ID：`GET /api/v1/tags/by-name/午餐/products`
2. 根据产品ID查询产品详情

### 场景3：生成一周的午餐时间段

1. 调用时间段生成接口：
   ```
   POST /api/v1/tags/by-name/午餐/time-periods?start_date=2024-01-01&end_date=2024-01-07
   ```
2. 获取返回的时间段列表，展示给用户选择

### 场景4：创建外卖订单并关联时间规则

1. 用户选择午餐时段（从场景3获取的时间段）
2. 创建订单时传入`rule_id`和`rule_item_id`
3. 系统记录订单关联的时间规则，用于后续的订单管理和统计

---

## 数据库变更

### 新增表：tag_rule_relations

标签与规则关联表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| tag_id | Integer | 标签ID |
| rule_id | Integer | 规则ID |

### delivery_orders 表新增字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| rule_id | Integer | 关联的时间规则ID |
| rule_item_id | Integer | 关联的时间规则项ID |

---

## 注意事项

1. 标签名称必须唯一
2. 时间规则的cron表达式格式必须正确
3. 生成时间段时，开始日期不能晚于结束日期
4. 外卖订单的`rule_id`和`rule_item_id`为可选字段
5. 如果规则项使用cron表达式，系统会自动根据日期范围生成多个时间段
6. 如果规则项使用固定时间，系统只返回一个时间段

