# 外卖订单打印功能使用说明

## 功能概述

本功能实现了通过飞鹅云打印机打印外卖订单的功能，支持按日期范围、订单ID或订单ID列表查询并打印订单。

## 配置说明

### 1. 环境变量配置

在 `.env` 文件中添加以下飞鹅云配置：

```bash
# 飞鹅云打印机配置
FEIEYUN_USER=your_feieyun_username          # 飞鹅云后台注册用户名
FEIEYUN_UKEY=your_feieyun_ukey              # 飞鹅云后台生成的UKEY
FEIEYUN_SN=your_printer_sn                  # 打印机编号
FEIEYUN_API_URL=https://api.feieyun.cn/Api/Open/  # API地址（默认值）
```

### 2. 获取飞鹅云配置信息

1. 注册飞鹅云账号：https://www.feieyun.com/
2. 登录后台，在"个人中心"获取 USER 和 UKEY
3. 在"打印机管理"中添加打印机，获取打印机编号（SN）

## API 接口说明

### 接口地址

```
POST /api/v1/wechat_mini_app/admin/delivery/print_delivery_orders
```

### 权限要求

需要小程序管理员权限（miniapp:manage）

### 请求参数

支持两种方式传参：
1. **Query 参数方式**：参数通过 URL 传递
2. **POST Body 方式**：参数通过 JSON body 传递

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| token | string | 是 | 管理员token（Header） |
| start_date | string | 否 | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 否 | 结束日期，格式：YYYY-MM-DD |
| order_id | integer | 否 | 单个订单ID |
| order_ids | array[integer] | 否 | 订单ID列表 |
| sn | string | 否 | 打印机编号（可选，默认使用配置） |
| times | integer | 否 | 打印次数，默认1次 |

**注意：**
- `start_date/end_date`、`order_id`、`order_ids` 三种查询方式至少提供一种
- Query 参数优先级高于 Body 参数

### 请求示例

#### 方式一：使用 Query 参数

##### 1. 按日期范围打印

```bash
curl -X POST "http://localhost:8000/api/v1/wechat_mini_app/admin/delivery/print_delivery_orders?start_date=2024-01-01&end_date=2024-01-31&times=1" \
  -H "token: your_admin_token"
```

##### 2. 按订单ID打印

```bash
curl -X POST "http://localhost:8000/api/v1/wechat_mini_app/admin/delivery/print_delivery_orders?order_id=123&times=1" \
  -H "token: your_admin_token"
```

##### 3. 按订单ID列表打印

```bash
curl -X POST "http://localhost:8000/api/v1/wechat_mini_app/admin/delivery/print_delivery_orders?order_ids=123&order_ids=124&order_ids=125&times=1" \
  -H "token: your_admin_token"
```

##### 4. 使用自定义打印机

```bash
curl -X POST "http://localhost:8000/api/v1/wechat_mini_app/admin/delivery/print_delivery_orders?start_date=2024-01-01&end_date=2024-01-31&sn=custom_printer_sn&times=2" \
  -H "token: your_admin_token"
```

#### 方式二：使用 POST Body（推荐）

##### 1. 按日期范围打印

```bash
curl -X POST "http://localhost:8000/api/v1/wechat_mini_app/admin/delivery/print_delivery_orders" \
  -H "Content-Type: application/json" \
  -H "token: your_admin_token" \
  -d '{
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "times": 1
  }'
```

##### 2. 按订单ID打印

```bash
curl -X POST "http://localhost:8000/api/v1/wechat_mini_app/admin/delivery/print_delivery_orders" \
  -H "Content-Type: application/json" \
  -H "token: your_admin_token" \
  -d '{
    "order_id": 123,
    "times": 1
  }'
```

##### 3. 按订单ID列表打印

```bash
curl -X POST "http://localhost:8000/api/v1/wechat_mini_app/admin/delivery/print_delivery_orders" \
  -H "Content-Type: application/json" \
  -H "token: your_admin_token" \
  -d '{
    "order_ids": [123, 124, 125],
    "times": 1
  }'
```

##### 4. 使用自定义打印机

```bash
curl -X POST "http://localhost:8000/api/v1/wechat_mini_app/admin/delivery/print_delivery_orders" \
  -H "Content-Type: application/json" \
  -H "token: your_admin_token" \
  -d '{
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "sn": "custom_printer_sn",
    "times": 2
  }'
```

### 响应示例

#### 成功响应

```json
{
    "code": 200,
    "message": "成功打印 2/2 个订单",
    "data": {
        "printed_count": 2,
        "total_count": 2,
        "print_results": [
            {
                "order_id": 123,
                "order_no": "O20240115120000123456",
                "success": true,
                "message": "打印成功",
                "print_order_id": "816501678_20240115184316_1419533539"
            },
            {
                "order_id": 124,
                "order_no": "O20240115130000123457",
                "success": true,
                "message": "打印成功",
                "print_order_id": "816501678_20240115184320_1419533540"
            }
        ]
    }
}
```

#### 部分成功响应

```json
{
    "code": 400,
    "message": "成功打印 1/2 个订单",
    "data": {
        "printed_count": 1,
        "total_count": 2,
        "print_results": [
            {
                "order_id": 123,
                "order_no": "O20240115120000123456",
                "success": true,
                "message": "打印成功",
                "print_order_id": "816501678_20240115184316_1419533539"
            },
            {
                "order_id": 124,
                "order_no": "O20240115130000123457",
                "success": false,
                "message": "打印内容超过5000字节限制"
            }
        ]
    }
}
```

#### 错误响应

```json
{
    "code": 400,
    "message": "必须提供查询条件（order_id、order_ids 或 日期范围）",
    "data": {
        "printed_count": 0,
        "total_count": 0,
        "print_results": []
    }
}
```

```json
{
    "code": 403,
    "message": "无权限访问",
    "data": null
}
```

## 打印内容格式

打印的小票内容包括：

1. **标题**：外卖订单（居中放大）
2. **订单信息**：订单号、下单时间
3. **用户信息**：收货人、联系电话
4. **配送信息**：配送地址、配送时间
5. **商品列表**：商品名称、单价、数量、金额
6. **金额汇总**：商品小计、配送费、优惠金额、应付金额、实付金额
7. **其他信息**：支付方式、订单状态、支付时间

### 打印示例

```
        外卖订单
--------------------------------
订单号：O20240115120000123456
下单时间：2024-01-15 12:00:00
--------------------------------
收货人：张三
联系电话：13800138000
配送地址：北京市朝阳区某某街道123号
配送时间：2024-01-15 12:30:00
--------------------------------
名称           单价  数量 金额
--------------------------------
宫保鸡丁       28.0  1    28.0
蛋炒饭         16.0  2    32.0
--------------------------------
商品小计：60.0元
配送费：8.5元
应付金额：68.5元
实付金额：68.5元

支付方式：微信支付
订单状态：已完成
```

## 代码使用示例

### Python 代码调用

```python
from app.service.feieyun import feieyun_service
from app.core.deps import get_db

# 获取数据库会话
db = next(get_db())

try:
    # 按日期范围打印
    result = feieyun_service.print_delivery_orders(
        db=db,
        start_date="2024-01-01",
        end_date="2024-01-31",
        times=1
    )
    
    print(f"打印结果: {result}")
    print(f"成功打印: {result['printed_count']}/{result['total_count']} 个订单")
    
    # 按订单ID打印
    result = feieyun_service.print_delivery_orders(
        db=db,
        order_id=123,
        times=1
    )
    
    # 按订单ID列表打印
    result = feieyun_service.print_delivery_orders(
        db=db,
        order_ids=[123, 124, 125],
        times=1
    )
    
finally:
    db.close()
```

## 注意事项

1. **打印内容限制**：单次打印内容不能超过 5000 字节
2. **打印机状态**：确保打印机在线且纸张充足
3. **网络连接**：确保服务器能访问飞鹅云 API（https://api.feieyun.cn）
4. **权限验证**：只有具有 miniapp:manage 权限的管理员才能调用打印接口
5. **错误处理**：如果部分订单打印失败，接口会返回详细的失败原因

## 常见问题

### 1. 打印失败：未配置打印机编号

**原因**：环境变量中未配置 `FEIEYUN_SN`，且接口调用时也未传入 `sn` 参数

**解决**：在 `.env` 文件中配置 `FEIEYUN_SN`，或在调用接口时传入 `sn` 参数

### 2. 打印失败：飞鹅云配置不完整

**原因**：`FEIEYUN_USER` 或 `FEIEYUN_UKEY` 未配置

**解决**：在 `.env` 文件中配置完整的飞鹅云信息

### 3. 打印内容超过5000字节限制

**原因**：订单商品过多或商品名称过长

**解决**：
- 减少单次打印的订单数量
- 简化商品名称
- 分批打印

### 4. API请求超时

**原因**：网络连接问题或飞鹅云服务响应慢

**解决**：
- 检查网络连接
- 稍后重试
- 联系飞鹅云客服

## 技术支持

如有问题，请联系：
- 飞鹅云官网：https://www.feieyun.com/
- 飞鹅云客服：查看官网联系方式

