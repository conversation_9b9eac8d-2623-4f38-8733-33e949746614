# 飞鹅云打印功能实现总结

## 功能概述

本次实现了通过飞鹅云打印机打印外卖订单的完整功能，包括：

1. **FeieyunService 打印服务**：核心打印逻辑
2. **管理员打印接口**：小程序管理员调用的 API 接口
3. **配置管理**：飞鹅云相关配置项
4. **文档和测试**：使用说明和测试脚本

## 文件改动清单

### 1. 配置文件

#### `app/core/config.py`
- 新增飞鹅云配置项：
  - `FEIEYUN_USER`: 飞鹅云用户名
  - `FEIEYUN_UKEY`: 飞鹅云密钥
  - `FEIEYUN_SN`: 打印机编号
  - `FEIEYUN_API_URL`: API地址

### 2. 服务层

#### `app/service/feieyun.py`
新增以下方法：

1. **`_generate_signature(stime: str) -> str`**
   - 生成飞鹅云 API 签名
   - 使用 SHA1 加密

2. **`_get_str_width(text: str) -> int`**
   - 计算字符串字节宽度
   - 中文2字节，英文1字节

3. **`_format_product_line(...) -> str`**
   - 格式化商品行
   - 处理长名称自动换行和对齐
   - 参考飞鹅云排版例子实现

4. **`_format_order_content(order: DeliveryOrder) -> str`**
   - 格式化订单打印内容
   - 包含订单信息、用户信息、配送信息、商品列表、金额汇总等

5. **`_call_print_api(sn: str, content: str, times: int) -> Dict`**
   - 调用飞鹅云打印 API
   - 处理 API 响应和错误

6. **`print_delivery_orders(...) -> Dict`**
   - 主打印方法
   - 支持按日期、订单ID、订单ID列表查询
   - 批量打印订单

### 3. API 接口层

#### `app/api/v1/wechat_mini_app/admin/delivery.py`
新增接口：

**`POST /api/v1/wechat_mini_app/admin/delivery/print_delivery_orders`**

特性：
- 需要管理员权限（miniapp:manage）
- 支持 Query 参数和 POST Body 两种传参方式
- 支持三种查询方式：日期范围、订单ID、订单ID列表
- 返回详细的打印结果

### 4. Schema 定义

#### `app/schemas/order.py`
新增 Schema：

**`PrintDeliveryOrdersRequest`**
- 定义打印请求的数据结构
- 支持 POST Body 传参

### 5. 文档

#### `doc/feieyun/外卖订单打印使用说明.md`
完整的使用文档，包括：
- 配置说明
- API 接口说明
- 请求示例（Query 和 Body 两种方式）
- 响应示例
- 打印内容格式
- 代码使用示例
- 常见问题

### 6. 测试脚本

#### `tests/service/test_feieyun_print.py`
测试脚本，包括：
- 按日期范围打印测试
- 按订单ID打印测试
- 按订单ID列表打印测试
- 使用自定义打印机测试

## 核心功能特性

### 1. 灵活的查询方式

支持三种查询方式：
- **日期范围**：`start_date` 和/或 `end_date`
- **单个订单**：`order_id`
- **订单列表**：`order_ids`

### 2. 双重传参支持

- **Query 参数**：适合简单场景
- **POST Body**：适合复杂参数，推荐使用

### 3. 智能排版

- 参考飞鹅云官方排版例子
- 自动处理长商品名称换行
- 保持各列对齐
- 支持58mm和80mm打印机

### 4. 完善的错误处理

- 配置检查
- 内容长度限制（5000字节）
- API 调用超时处理
- 详细的错误日志

### 5. 批量打印

- 支持一次打印多个订单
- 返回每个订单的打印结果
- 部分失败不影响其他订单

## 使用示例

### 配置环境变量

```bash
# .env 文件
FEIEYUN_USER=your_username
FEIEYUN_UKEY=your_ukey
FEIEYUN_SN=your_printer_sn
```

### API 调用示例

#### 使用 POST Body（推荐）

```bash
curl -X POST "http://localhost:8000/api/v1/wechat_mini_app/admin/delivery/print_delivery_orders" \
  -H "Content-Type: application/json" \
  -H "token: your_admin_token" \
  -d '{
    "order_id": 1619,
    "times": 1
  }'
```

#### 使用 Query 参数

```bash
curl -X POST "http://localhost:8000/api/v1/wechat_mini_app/admin/delivery/print_delivery_orders?order_id=1619&times=1" \
  -H "token: your_admin_token"
```

### Python 代码调用

```python
from app.service.feieyun import feieyun_service
from app.core.deps import get_db

db = next(get_db())

try:
    result = feieyun_service.print_delivery_orders(
        db=db,
        order_id=1619,
        times=1
    )
    print(f"打印结果: {result}")
finally:
    db.close()
```

## 打印内容示例

```
        外卖订单
--------------------------------
订单号：O20240115120000123456
下单时间：2024-01-15 12:00:00
--------------------------------
收货人：张三
联系电话：13800138000
配送地址：北京市朝阳区某某街道123号
配送时间：2024-01-15 12:30:00
--------------------------------
名称           单价  数量 金额
--------------------------------
宫保鸡丁       28.0  1    28.0
蛋炒饭         16.0  2    32.0
--------------------------------
商品小计：60.0元
配送费：8.5元
应付金额：68.5元
实付金额：68.5元

支付方式：微信支付
订单状态：已完成
```

## 注意事项

1. **权限要求**：只有具有 `miniapp:manage` 权限的管理员才能调用打印接口
2. **配置完整性**：确保配置了 `FEIEYUN_USER`、`FEIEYUN_UKEY` 和 `FEIEYUN_SN`
3. **内容限制**：单次打印内容不能超过 5000 字节
4. **网络连接**：确保服务器能访问飞鹅云 API
5. **打印机状态**：确保打印机在线且纸张充足

## 技术亮点

1. **参考官方示例**：排版逻辑参考飞鹅云官方 PHP 示例，确保兼容性
2. **字节精确计算**：使用 GBK 编码计算字节数，确保排版准确
3. **智能换行对齐**：长商品名称自动换行并保持对齐
4. **双重传参支持**：同时支持 Query 和 Body 传参，灵活方便
5. **详细日志记录**：完整的日志记录，便于问题排查

## 后续优化建议

1. **打印模板配置化**：将打印格式做成可配置的模板
2. **打印历史记录**：记录打印历史，便于追溯
3. **打印预览**：提供打印预览功能
4. **多打印机支持**：支持配置多台打印机，按需选择
5. **异步打印**：对于大批量打印，使用异步任务处理

## 相关文档

- [外卖订单打印使用说明](./外卖订单打印使用说明.md)
- [飞鹅云 API 文档](./feieyun.md)
- [飞鹅云 Python 示例](./api_python_demo.py)
- [飞鹅云排版例子](./排版例子.php)

