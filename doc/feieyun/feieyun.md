打印订单，Open_printMsg

功能描述：
发送用户需要打印的订单内容给飞鹅云小票设备、打印扫码一体机 （该接口只能是小票机使用，如购买标签机请使用标签机专用接口）

请求URL：
https://api.feieyun.cn/Api/Open/


请求方式：
POST(推荐)，GET


特别注意：
开发者自己写的请求以表单方式提交数据，请求头需要使用Content-Type: application/x-www-form-urlencoded，请勿使用其它方式提交数据


公共参数：
请求参数名	是否必须	类型	说明
user 	必须	string	飞鹅云后台注册用户名。
stime 	必须	int	当前UNIX时间戳，10位，精确到秒。
backurl 		string	必须先在管理后台设置，回调数据格式详见《订单状态回调》
expired 		int	订单失效UNIX时间戳，10位，精确到秒，打印时超过该时间该订单将抛弃不打印，取值范围为：当前时间<订单失效时间≤24小时后。
sig 	必须	string	对参数user+UKEY+stime 拼接后（+号表示连接符）进行SHA1加密得到签名，加密后签名值为40位小写字符串。
apiname 	必须	string	请求的接口名称：Open_printMsg


私有参数：
请求参数名	是否必须	类型	说明
sn 	必须	string	设备编号
content 	必须	string	打印内容,不能超过5000字节
times 		int	打印次数，默认为1。


排版控制标签说明：(详情请参考SDK示例)
<BR> ：换行符
<CUT> ：切刀指令(主动切纸,仅限切刀设备使用才有效果)
<LOGO> ：打印LOGO指令(前提是预先在机器内置LOGO图片)
<PLUGIN> ：钱箱
<CB></CB> ：居中放大
<B></B> ：放大一倍
<C></C> ：居中
<L></L> ：字体变高一倍
<W></W> ：字体变宽一倍
<QR></QR> ：二维码（单个订单，最多只能打印一个二维码）
<RIGHT></RIGHT> ：右对齐
<BOLD></BOLD> ：字体加粗
说明：来订单时默认播放新来单语音，若使用"申请退单"或"申请取消订单"的语音，请使用以下指令
<AUDIO-REFUND> ：申请退单语音指令。播报内容为：有用户申请退单了
<AUDIO-CANCEL> ：申请取消订单语音指令。播报内容为：有用户申请取消订单了
说明：条形码标签仅支持以下标签规定的内容打印
<BC128_A>123ABCDEF</BC128_A> ：数字和大写字母混合的条形码，最多支持14位的数字、大写字母混合条形码
<BC128_B>123ABCDef</BC128_B> ：数字和大小写字母混合的条形码，最多支持14位的数字、大写字母、小写字母混合条形码
<BC128_C>0123456789</BC128_C> ：最多支持22位纯数字


返回参数说明：
返回参数名	类型	说明
data 	string	正确返回订单ID。

返回示例：


正确：
{
"msg":"ok",
"ret":0,
"data":"816501678_20160919184316_1419533539",
"serverExecutedTime":3
}


错误：

{
"msg":"参数错误 : 该帐号未注册.",
"ret": -2 ,
"data": null ,
"serverExecutedTime": 37
}

