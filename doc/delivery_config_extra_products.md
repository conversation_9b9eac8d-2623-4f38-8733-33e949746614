# 外卖配置 API - 额外产品功能实现

## 概述

本文档描述了对 `/delivery/config` API 的增强，添加了根据 `extra_product_ids` 获取额外产品信息的功能。

## API 端点

**GET** `/api/v1/wechat_mini_app/delivery/config`

### 请求参数

| 参数名 | 类型 | 位置 | 必填 | 说明 |
|--------|------|------|------|------|
| token | string | Header | 是 | 用户认证 token |

### 响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "delivery_lunch": {...},
    "delivery_dinner": {...},
    "extra_products": [...]
  }
}
```

## 修改内容

### 1. 导入 product_dao

```python
from app.dao.product import product_dao
```

### 2. 获取额外产品信息

在原有逻辑基础上，添加了根据 `extra_product_ids` 获取产品详细信息的功能：

```python
# 根据extra_product_ids获取产品信息
extra_products = []
if extra_product_ids:
    for product_id in extra_product_ids:
        product = product_dao.get(db, product_id)
        if product:
            extra_products.append({
                "id": product.id,
                "name": product.name,
                "price": product.price,
                "description": product.description,
                "image": product.image,
                "stock": product.stock,
                "status": product.status.value if product.status else "active",
                "type": product.type.value if product.type else "product",
                "meal_type": product.meal_type.value if product.meal_type else "buffet",
                "listed_at": product.listed_at.strftime("%Y-%m-%d %H:%M:%S") if product.listed_at else None,
                "created_at": product.created_at.strftime("%Y-%m-%d %H:%M:%S") if product.created_at else None,
                "updated_at": product.updated_at.strftime("%Y-%m-%d %H:%M:%S") if product.updated_at else None
            })
```

### 3. 修复数据结构问题

- 修复了 `"deliverrulesy_lunch"` 拼写错误为 `"delivery_lunch"`
- 修复了空字符串键 `""` 为 `"rules"`
- 为 `delivery_dinner` 添加了 `rule_times` 字段（使用 `rule.get("rule_times", [])`）

## 数据结构

### extra_products 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 产品ID |
| name | string | 产品名称 |
| price | float | 产品价格 |
| description | string | 产品描述 |
| image | string | 产品图片URL |
| stock | int | 库存数量 |
| status | string | 产品状态（active/inactive） |
| type | string | 产品类型（product/direct_sale/reservation/virtual） |
| meal_type | string | 餐食类型（business/buffet/coupon） |
| listed_at | string | 上架时间（格式：YYYY-MM-DD HH:MM:SS） |
| created_at | string | 创建时间（格式：YYYY-MM-DD HH:MM:SS） |
| updated_at | string | 更新时间（格式：YYYY-MM-DD HH:MM:SS） |

## 使用场景

额外产品（extra_products）通常用于：

1. **饮料类产品**：可乐、雪碧、矿泉水等
2. **小吃类产品**：薯片、饼干等
3. **配菜类产品**：额外的配菜或调料
4. **其他附加产品**：餐具、纸巾等

这些产品不属于主餐（午餐或晚餐），但可以作为附加选项供用户选择。

## 标签配置

需要在系统中配置以下标签：

- `delivery_lunch`: 午餐外卖产品标签
- `delivery_dinner`: 晚餐外卖产品标签
- `delivery_extra`: 额外产品标签

## 完整响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "delivery_lunch": {
      "rules": [
        {
          "rule_id": 1,
          "rule_name": "午餐外卖规则",
          "rule_times": [...],
          "rule_items": [...]
        }
      ],
      "product_ids": [101, 102, 103]
    },
    "delivery_dinner": {
      "rules": [
        {
          "rule_id": 2,
          "rule_name": "晚餐外卖规则",
          "rule_times": [...],
          "rule_items": [...]
        }
      ],
      "product_ids": [201, 202, 203]
    },
    "extra_products": [
      {
        "id": 301,
        "name": "可乐",
        "price": 5.0,
        "description": "冰镇可乐 330ml",
        "image": "/static/uploads/products/cola.jpg",
        "stock": 100,
        "status": "active",
        "type": "product",
        "meal_type": "buffet",
        "listed_at": "2025-01-01 00:00:00",
        "created_at": "2025-01-01 00:00:00",
        "updated_at": "2025-09-30 10:00:00"
      },
      {
        "id": 302,
        "name": "雪碧",
        "price": 5.0,
        "description": "冰镇雪碧 330ml",
        "image": "/static/uploads/products/sprite.jpg",
        "stock": 100,
        "status": "active",
        "type": "product",
        "meal_type": "buffet",
        "listed_at": "2025-01-01 00:00:00",
        "created_at": "2025-01-01 00:00:00",
        "updated_at": "2025-09-30 10:00:00"
      }
    ]
  }
}
```

完整示例请参考：`doc/delivery_config_api_response_example.json`

## 实现逻辑

1. **获取标签关联的产品ID列表**
   ```python
   extra_product_ids = tag_service.get_product_ids_by_tag_name(db, 'delivery_extra')
   ```

2. **遍历产品ID，获取产品详情**
   ```python
   for product_id in extra_product_ids:
       product = product_dao.get(db, product_id)
       if product:
           # 构建产品信息字典
           extra_products.append({...})
   ```

3. **返回产品列表**
   - 只返回存在的产品（通过 `if product:` 过滤）
   - 格式化时间字段为字符串
   - 转换枚举类型为字符串值

## 错误处理

- 如果 `extra_product_ids` 为空，返回空列表 `[]`
- 如果某个产品ID对应的产品不存在，跳过该产品
- 所有时间字段都进行了 None 检查，避免格式化错误

## 性能考虑

当前实现使用循环逐个查询产品，如果 `extra_product_ids` 数量较多，可以考虑优化为批量查询：

```python
# 优化建议（未实现）
if extra_product_ids:
    products = session.query(Product).filter(Product.id.in_(extra_product_ids)).all()
    for product in products:
        extra_products.append({...})
```

## 相关文件

- `app/api/v1/wechat_mini_app/delivery/config.py`: 主要实现文件
- `app/dao/product.py`: 产品数据访问层
- `app/service/tag.py`: 标签服务
- `doc/delivery_config_api_response_example.json`: 完整响应示例

## 测试建议

1. **测试空标签**：确保 `delivery_extra` 标签不存在或为空时，返回空列表
2. **测试不存在的产品**：确保产品ID对应的产品不存在时，不会报错
3. **测试多个产品**：确保能正确返回多个产品信息
4. **测试产品字段**：确保所有产品字段都正确返回和格式化

## 更新日志

- **2025-09-30**: 初始实现
  - 添加 `extra_products` 字段
  - 根据 `extra_product_ids` 获取产品详细信息
  - 修复 `delivery_lunch` 和 `delivery_dinner` 数据结构问题
  - 添加 `rule_times` 字段支持

