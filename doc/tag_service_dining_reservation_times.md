# Tag Service - DINING_RESERVATION 时间生成功能

## 概述

本文档描述了对 `app/service/tag.py` 中 `get_rules_with_items_by_tag_name` 方法的增强，为 `DINING_RESERVATION` 类型的规则添加了自动时间生成功能。

## 功能说明

当规则类型为 `DINING_RESERVATION` 时，系统会：

1. 获取 `dining_reservation_rules` 表中的规则详情
2. 根据 cron 表达式生成指定数量的时间值
3. 计算订单截止时间和取消截止时间
4. 为规则（rule）和规则项（rule_item）分别生成时间信息

## 修改内容

### 1. 导入新的模型类

```python
from app.models.rule import Rule, RuleItem, RuleType, DiningReservationRule
```

### 2. 增强 `get_rules_with_items_by_tag_name` 方法

在原有功能基础上，添加了对 `DINING_RESERVATION` 类型规则的特殊处理：

- 为规则添加 `rule_times` 字段
- 为规则项添加 `rule_items_times` 字段

### 3. 新增辅助方法

#### `_generate_rule_times(dining_rule: DiningReservationRule)`

根据 `DiningReservationRule` 生成时间列表，包括：

- **输入参数**：
  - `dining_start_time_cron_str`: 就餐开始时间 cron 表达式
  - `dining_end_time_cron_str`: 就餐结束时间 cron 表达式
  - `verify_start_time_cron_str`: 核销开始时间 cron 表达式
  - `verify_end_time_cron_str`: 核销结束时间 cron 表达式
  - `order_deadline`: 订单截止提前量（分钟）
  - `cancellation_deadline`: 取消截止提前量（分钟）
  - `generated_count`: 生成数量

- **输出**：生成的时间列表，每个时间段包含：
  - `dining_start_time`: 就餐开始时间
  - `dining_end_time`: 就餐结束时间
  - `verify_start_time`: 核销开始时间
  - `verify_end_time`: 核销结束时间
  - `order_deadline`: 订单截止时间（从 dining_start_time 减去 order_deadline 分钟）
  - `cancel_deadline`: 取消截止时间（从 dining_start_time 减去 cancellation_deadline 分钟）

#### `_generate_rule_item_times(rule_item: RuleItem)`

根据 `RuleItem` 生成时间列表，包括：

- **输入参数**：
  - `start_time_cron_str`: 开始时间 cron 表达式
  - `end_time_cron_str`: 结束时间 cron 表达式
  - `order_deadline`: 订单截止提前量（分钟）
  - `cancellation_deadline`: 取消截止提前量（分钟）
  - `generated_count`: 生成数量

- **输出**：生成的时间列表，每个时间段包含：
  - `dining_start_time`: 就餐开始时间
  - `dining_end_time`: 就餐结束时间
  - `order_deadline`: 订单截止时间（从 dining_start_time 减去 order_deadline 分钟）
  - `cancel_deadline`: 取消截止时间（从 dining_start_time 减去 cancellation_deadline 分钟）

#### `_get_next_times_from_cron(cron_str: str, start_time: datetime, count: int)`

从 cron 表达式生成指定数量的时间点。

## 返回数据格式

### 规则级别（rule_times）

```json
{
  "rule_id": 1,
  "rule_name": "午餐订阅",
  "rule_times": [
    {
      "dining_start_time": "2025-01-02 11:30",
      "dining_end_time": "2025-01-02 14:00",
      "verify_start_time": "2025-01-02 11:00",
      "verify_end_time": "2025-01-02 14:30",
      "order_deadline": "2025-01-01 21:00",
      "cancel_deadline": "2025-01-02 09:00"
    },
    {
      "dining_start_time": "2025-01-03 11:30",
      "dining_end_time": "2025-01-03 14:00",
      "verify_start_time": "2025-01-03 11:00",
      "verify_end_time": "2025-01-03 14:30",
      "order_deadline": "2025-01-02 21:00",
      "cancel_deadline": "2025-01-03 09:00"
    }
  ]
}
```

### 规则项级别（rule_items_times）

```json
{
  "rule_items": [
    {
      "id": 17,
      "rule_id": 7,
      "name": "（时段2）11:45-12:00",
      "alias": null,
      "start_time_cron_str": "45 11 * * 0,1,2,3,4,5,6 0 *",
      "end_time_cron_str": "0 12 * * 0,1,2,3,4,5,6 0 *",
      "order_deadline": 1005,
      "cancellation_deadline": 135,
      "generated_count": 5,
      "quantity": 15,
      "meal_type": "lunch",
      "rule_items_times": [
        {
          "dining_start_time": "2025-01-02 11:45",
          "dining_end_time": "2025-01-02 12:00",
          "order_deadline": "2025-01-01 21:00",
          "cancel_deadline": "2025-01-02 09:30"
        },
        {
          "dining_start_time": "2025-01-03 11:45",
          "dining_end_time": "2025-01-03 12:00",
          "order_deadline": "2025-01-02 21:00",
          "cancel_deadline": "2025-01-03 09:30"
        }
      ]
    }
  ]
}
```

## 时间计算逻辑

### 订单截止时间（order_deadline）

```
order_deadline_time = dining_start_time - order_deadline (分钟)
```

例如：
- 就餐开始时间：2025-01-02 11:30
- 订单截止提前量：870 分钟（14.5 小时）
- 订单截止时间：2025-01-01 21:00

### 取消截止时间（cancel_deadline）

```
cancel_deadline_time = dining_start_time - cancellation_deadline (分钟)
```

例如：
- 就餐开始时间：2025-01-02 11:30
- 取消截止提前量：150 分钟（2.5 小时）
- 取消截止时间：2025-01-02 09:00

## 使用示例

```python
from app.db.session import SessionLocal
from app.service.tag import tag_service

db = SessionLocal()

# 获取标签关联的规则及其时间信息
result = tag_service.get_rules_with_items_by_tag_name(db, "商务餐")

for rule in result:
    print(f"规则: {rule['name']}")
    
    # 如果是 DINING_RESERVATION 类型，会包含 rule_times
    if 'rule_times' in rule:
        for time_info in rule['rule_times']:
            print(f"  就餐时间: {time_info['dining_start_time']} - {time_info['dining_end_time']}")
            print(f"  订单截止: {time_info['order_deadline']}")
            print(f"  取消截止: {time_info['cancel_deadline']}")
    
    # 规则项也会包含 rule_items_times
    for item in rule['rule_items']:
        if 'rule_items_times' in item:
            print(f"  规则项: {item['name']}")
            for time_info in item['rule_items_times']:
                print(f"    时间段: {time_info['dining_start_time']} - {time_info['dining_end_time']}")

db.close()
```

## 测试

运行测试脚本：

```bash
cd /Users/<USER>/Project/yihes/yh-vegan-admin
poetry run python test_tag_service_dining_reservation.py
```

## 注意事项

1. **Cron 表达式验证**：确保 cron 表达式格式正确，否则会返回空列表
2. **生成数量**：如果 `generated_count` 为 None，默认生成 5 个时间段
3. **时间格式**：所有时间都格式化为 `YYYY-MM-DD HH:MM` 格式
4. **异常处理**：如果 cron 解析失败，会打印错误信息并返回空列表
5. **非 DINING_RESERVATION 规则**：对于其他类型的规则，不会生成 `rule_times` 和 `rule_items_times` 字段

## 相关文件

- `app/service/tag.py`: 主要实现文件
- `app/models/rule.py`: 规则模型定义
- `app/dao/rule.py`: 规则数据访问层
- `test_tag_service_dining_reservation.py`: 测试脚本

## 更新日志

- **2025-09-30**: 初始实现，添加 DINING_RESERVATION 规则的时间生成功能

