# 蜂鸟快递下单逻辑集成总结

## 概述

根据用户需求，在外卖订单支付成功后集成蜂鸟快递下单逻辑，包括非微信支付和微信支付两种场景的处理。

## 实现的功能

### 1. 数据库模型扩展

#### 1.1 DeliveryOrder模型新增字段
- `service_goods_id`: 蜂鸟服务商品ID
- `base_goods_id`: 蜂鸟基础商品ID  
- `t_index_id`: 蜂鸟预询标识

#### 1.2 对应Schema更新
- `DeliveryOrderCreate`
- `DeliveryOrderUpdate`

#### 1.3 数据库迁移
- 创建了迁移文件 `alembic/versions/add_fengniao_fields_to_delivery_order.py`

### 2. 非微信支付场景（余额支付、企业支付）

#### 2.1 修改文件
- `/app/api/v1/wechat_mini_app/order.py`

#### 2.2 实现逻辑
在以下支付成功后添加蜂鸟下单逻辑：
- 个人账户余额支付
- 企业账户支付
- 商务餐企业支付

#### 2.3 处理流程
1. 检查订单类型是否为`OrderType.DELIVERY`
2. 验证蜂鸟必要参数（delivery_fee、service_goods_id、base_goods_id、t_index_id）
3. 调用蜂鸟下单接口
4. 检查返回结果：
   - 成功：更新订单的蜂鸟订单号和状态
   - 失败（B0109错误码）：返回"配送费发生变动，支付失败"
   - 其他失败：返回具体错误信息

### 3. 微信支付场景

#### 3.1 修改文件
- `/app/api/v1/wechat_mini_app/payment.py`

#### 3.2 实现逻辑
在微信支付回调成功处理中添加蜂鸟下单逻辑：

#### 3.3 处理流程
1. 在`handle_payment_success`函数中添加`OrderType.DELIVERY`分支
2. 微信支付成功后调用蜂鸟下单接口
3. 检查返回结果：
   - 成功：更新订单的蜂鸟订单号和状态
   - 失败（B0109错误码）：发起退款操作
   - 其他失败：记录错误日志（不影响微信支付成功状态）

### 4. 蜂鸟下单接口封装

#### 4.1 辅助函数
- `_create_fengniao_order()`: 用于非微信支付场景
- `_create_fengniao_order_for_wechat_payment()`: 用于微信支付场景
- `_initiate_refund_for_delivery_fee_change()`: 处理配送费变动退款

#### 4.2 蜂鸟API调用
- 使用`FengniaoClient.create_order()`方法
- 构建完整的订单数据，包括商品列表、配送信息等
- 处理价格验证（actual_delivery_amount_cent、pre_create_order_t_index_id）

### 5. 外卖订单创建优化

#### 5.1 修改文件
- `/app/api/v1/wechat_mini_app/delivery.py`

#### 5.2 改进内容
- 在创建外卖订单时保存蜂鸟预下单参数
- 确保后续支付时能够获取到这些参数

## 错误处理

### 1. 配送费变动处理
- **非微信支付**：返回错误信息，阻止支付完成
- **微信支付**：发起退款操作，更新订单状态为已取消

### 2. 其他蜂鸟API错误
- 记录详细错误日志
- 非微信支付场景：返回具体错误信息
- 微信支付场景：不影响支付成功状态，仅记录日志

### 3. 异常处理
- 所有蜂鸟API调用都包含try-catch异常处理
- 详细的错误日志记录，便于问题排查

## 测试

### 1. 测试脚本
- `test_fengniao_order_integration.py`: 集成测试脚本
- 测试外卖订单创建、余额支付、微信支付参数生成

### 2. 测试场景
- 正常蜂鸟下单流程
- 配送费变动场景
- 蜂鸟API异常场景
- 缺少必要参数场景

## 配置要求

### 1. 环境变量
确保以下蜂鸟配置正确设置：
- `FENGNIAO_APP_KEY`
- `FENGNIAO_APP_SECRET`
- `FENGNIAO_API_URL`
- `FENGNIAO_MERCHANT_ID`

### 2. 数据库迁移
运行数据库迁移以添加新字段：
```bash
poetry run alembic upgrade head
```

## 注意事项

### 1. 事务处理
- 蜂鸟下单失败不会回滚已完成的支付
- 微信支付成功后的蜂鸟下单失败会触发退款流程

### 2. 日志记录
- 所有蜂鸟API调用都有详细的日志记录
- 包含请求参数、响应结果、错误信息等

### 3. 性能考虑
- 蜂鸟API调用是同步的，可能影响支付响应时间
- 建议监控API调用耗时，必要时考虑异步处理

## 配送模式配置集成

### 1. 配送模式支持
根据用户要求，已集成delivery.py中的配送模式配置逻辑：

#### 1.1 门店发单模式（store）
- 优先使用`FENGNIAO_CHAIN_STORE_ID`（如果配置）
- 备选使用`FENGNIAO_SHOP_ID`作为`out_shop_code`
- 适用于有实体门店的商户

#### 1.2 点对点发单模式（point_to_point）
- 使用取货点坐标信息：
  - `FENGNIAO_TRANSPORT_LONGITUDE`：取货点经度
  - `FENGNIAO_TRANSPORT_LATITUDE`：取货点纬度
  - `FENGNIAO_TRANSPORT_ADDRESS`：取货点地址描述
  - `FENGNIAO_TRANSPORT_TEL`：取货点联系电话
- 适用于无固定门店的商户

### 2. 配置参数
```bash
# 配送模式选择
FENGNIAO_DELIVERY_MODE=store  # 或 point_to_point

# 门店发单模式参数
FENGNIAO_SHOP_ID=461841708
FENGNIAO_CHAIN_STORE_ID=461841708  # 可选

# 点对点发单模式参数
FENGNIAO_TRANSPORT_LONGITUDE=113.431943
FENGNIAO_TRANSPORT_LATITUDE=23.095987
FENGNIAO_TRANSPORT_ADDRESS=广州市黄埔区黄埔东路976号
FENGNIAO_TRANSPORT_TEL=18802053864
```

### 3. 实现细节

#### 3.1 辅助函数
- `_add_delivery_mode_params()`: order.py中使用
- `_add_delivery_mode_params_for_payment()`: payment.py中使用
- 两个函数逻辑相同，分别用于不同模块避免循环导入

#### 3.2 日志记录
- 详细记录使用的配送模式和参数
- 便于调试和问题排查

#### 3.3 默认行为
- 如果配置了未知的配送模式，默认使用门店发单模式
- 确保系统的健壮性

## 后续优化建议

1. **异步处理**：考虑将蜂鸟下单改为异步处理，提高支付响应速度
2. **重试机制**：添加蜂鸟API调用失败的重试机制
3. **监控告警**：添加蜂鸟下单失败的监控和告警
4. **退款优化**：完善微信支付退款的具体实现
5. **状态同步**：定期同步蜂鸟订单状态到本地数据库
6. **配送模式动态切换**：考虑支持运行时动态切换配送模式
