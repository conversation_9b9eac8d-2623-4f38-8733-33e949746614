{"code": 200, "message": "success", "data": {"delivery_lunch": {"rules": [{"rule_id": 1, "rule_name": "午餐外卖规则", "rule_times": [{"dining_start_time": "2025-10-01 11:30", "dining_end_time": "2025-10-01 14:00", "verify_start_time": "2025-10-01 11:00", "verify_end_time": "2025-10-01 14:30", "order_deadline": "2025-09-30 21:00", "cancel_deadline": "2025-10-01 09:00"}, {"dining_start_time": "2025-10-02 11:30", "dining_end_time": "2025-10-02 14:00", "verify_start_time": "2025-10-02 11:00", "verify_end_time": "2025-10-02 14:30", "order_deadline": "2025-10-01 21:00", "cancel_deadline": "2025-10-02 09:00"}], "rule_items": [{"id": 1, "rule_id": 1, "name": "午餐时段1", "alias": "第一批次", "start_time_cron_str": "30 11 * * 1,2,3,4,5 0 *", "end_time_cron_str": "45 11 * * 1,2,3,4,5 0 *", "order_deadline": 870, "cancellation_deadline": 150, "generated_count": 5, "quantity": 20, "meal_type": "lunch", "rule_items_times": [{"dining_start_time": "2025-10-01 11:30", "dining_end_time": "2025-10-01 11:45", "order_deadline": "2025-09-30 21:00", "cancel_deadline": "2025-10-01 09:00"}, {"dining_start_time": "2025-10-02 11:30", "dining_end_time": "2025-10-02 11:45", "order_deadline": "2025-10-01 21:00", "cancel_deadline": "2025-10-02 09:00"}]}]}], "product_ids": [101, 102, 103]}, "delivery_dinner": {"rules": [{"rule_id": 2, "rule_name": "晚餐外卖规则", "rule_times": [{"dining_start_time": "2025-10-01 17:30", "dining_end_time": "2025-10-01 20:00", "verify_start_time": "2025-10-01 17:00", "verify_end_time": "2025-10-01 20:30", "order_deadline": "2025-10-01 09:00", "cancel_deadline": "2025-10-01 15:00"}, {"dining_start_time": "2025-10-02 17:30", "dining_end_time": "2025-10-02 20:00", "verify_start_time": "2025-10-02 17:00", "verify_end_time": "2025-10-02 20:30", "order_deadline": "2025-10-02 09:00", "cancel_deadline": "2025-10-02 15:00"}], "rule_items": [{"id": 2, "rule_id": 2, "name": "晚餐时段1", "alias": "第一批次", "start_time_cron_str": "30 17 * * 1,2,3,4,5 0 *", "end_time_cron_str": "45 17 * * 1,2,3,4,5 0 *", "order_deadline": 510, "cancellation_deadline": 150, "generated_count": 5, "quantity": 20, "meal_type": "dinner", "rule_items_times": [{"dining_start_time": "2025-10-01 17:30", "dining_end_time": "2025-10-01 17:45", "order_deadline": "2025-10-01 09:00", "cancel_deadline": "2025-10-01 15:00"}, {"dining_start_time": "2025-10-02 17:30", "dining_end_time": "2025-10-02 17:45", "order_deadline": "2025-10-02 09:00", "cancel_deadline": "2025-10-02 15:00"}]}]}], "product_ids": [201, 202, 203]}, "extra_products": [{"id": 301, "name": "可乐", "price": 5.0, "description": "冰镇可乐 330ml", "image": "/static/uploads/products/cola.jpg", "stock": 100, "status": "active", "type": "product", "meal_type": "buffet", "listed_at": "2025-01-01 00:00:00", "created_at": "2025-01-01 00:00:00", "updated_at": "2025-09-30 10:00:00"}, {"id": 302, "name": "雪碧", "price": 5.0, "description": "冰镇雪碧 330ml", "image": "/static/uploads/products/sprite.jpg", "stock": 100, "status": "active", "type": "product", "meal_type": "buffet", "listed_at": "2025-01-01 00:00:00", "created_at": "2025-01-01 00:00:00", "updated_at": "2025-09-30 10:00:00"}, {"id": 303, "name": "矿泉水", "price": 2.0, "description": "农夫山泉 550ml", "image": "/static/uploads/products/water.jpg", "stock": 200, "status": "active", "type": "product", "meal_type": "buffet", "listed_at": "2025-01-01 00:00:00", "created_at": "2025-01-01 00:00:00", "updated_at": "2025-09-30 10:00:00"}]}}