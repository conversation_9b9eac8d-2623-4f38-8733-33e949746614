# 标签与时间规则关联功能

## 功能概述

本功能实现了标签（Tag）与时间规则（Rule）的关联管理，允许通过标签来组织和管理时间规则，并支持根据标签和时间规则生成日期时间段。同时，外卖订单可以关联时间规则项，便于订单管理和统计分析。

## 核心功能

### 1. 标签与规则关联
- ✅ 标签可以关联多个时间规则
- ✅ 时间规则可以关联多个标签
- ✅ 支持批量绑定和解绑操作
- ✅ 支持通过标签名称快速查询关联的规则

### 2. 资源获取
- ✅ 根据标签名称获取产品ID列表
- ✅ 根据标签名称获取规则ID列表
- ✅ 根据标签名称获取完整的规则及规则项信息

### 3. 时间段生成
- ✅ 支持基于Cron表达式的时间段生成
- ✅ 支持基于固定时间的时间段生成
- ✅ 可指定日期范围生成多个时间段
- ✅ 返回完整的规则信息（订购截止时间、取消截止时间、数量等）

### 4. 外卖订单关联
- ✅ 外卖订单可以关联时间规则
- ✅ 外卖订单可以关联具体的时间规则项
- ✅ 便于后续的订单管理和统计分析

## 快速开始

### 1. 数据库迁移

首先运行数据库迁移以创建新的表和字段：

```bash
cd /Users/<USER>/Project/yihes/yh-vegan-admin
poetry run alembic upgrade head
```

### 2. 创建标签

```bash
curl -X POST "http://localhost:8000/api/v1/tags/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "午餐"
  }'
```

### 3. 绑定标签到规则

```bash
curl -X POST "http://localhost:8000/api/v1/tags/1/bind/rules" \
  -H "Content-Type: application/json" \
  -d '{
    "rule_ids": [1, 2]
  }'
```

### 4. 获取标签关联的规则

```bash
curl -X GET "http://localhost:8000/api/v1/tags/by-name/午餐/rules/with-items"
```

### 5. 生成时间段

```bash
curl -X POST "http://localhost:8000/api/v1/tags/by-name/午餐/time-periods?start_date=2024-01-01&end_date=2024-01-07"
```

## API接口说明

### 标签与规则绑定

#### 绑定规则到标签
```
POST /api/v1/tags/{tag_id}/bind/rules
```

请求体：
```json
{
  "rule_ids": [1, 2, 3]
}
```

#### 解绑规则
```
POST /api/v1/tags/{tag_id}/unbind/rules
```

请求体：
```json
{
  "rule_ids": [1, 2]
}
```

### 资源查询

#### 获取产品ID列表
```
GET /api/v1/tags/by-name/{tag_name}/products
```

响应：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tag_name": "午餐",
    "product_ids": [1, 2, 3]
  }
}
```

#### 获取规则ID列表
```
GET /api/v1/tags/by-name/{tag_name}/rules
```

响应：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tag_name": "午餐",
    "rule_ids": [1, 2]
  }
}
```

#### 获取规则及规则项
```
GET /api/v1/tags/by-name/{tag_name}/rules/with-items
```

响应：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tag_name": "午餐",
    "rules": [
      {
        "id": 1,
        "name": "工作日午餐规则",
        "rule_items": [...]
      }
    ]
  }
}
```

### 时间段生成

```
POST /api/v1/tags/by-name/{tag_name}/time-periods?start_date=2024-01-01&end_date=2024-01-07
```

响应：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tag_name": "午餐",
    "periods": [
      {
        "rule_id": 1,
        "rule_item_id": 1,
        "start_time": "2024-01-01 11:30:00",
        "end_time": "2024-01-01 13:30:00",
        "meal_type": "lunch"
      }
    ]
  }
}
```

## 使用场景

### 场景1：餐厅午餐时段管理

1. 创建"午餐"标签
2. 创建"工作日午餐规则"，设置时间为11:30-13:30
3. 将标签绑定到午餐相关的产品
4. 将标签绑定到午餐时间规则
5. 前端通过标签获取可用产品和时间段

### 场景2：外卖订单时间管理

1. 用户选择午餐时段
2. 系统根据标签生成可用的时间段列表
3. 用户选择具体的时间段
4. 创建订单时关联对应的rule_id和rule_item_id
5. 系统可以根据时间规则进行订单管理

### 场景3：多时段产品管理

1. 创建多个时段标签（早餐、午餐、晚餐）
2. 为每个时段创建对应的时间规则
3. 产品可以关联多个标签（如某产品同时适用于午餐和晚餐）
4. 通过标签快速筛选不同时段的产品

## 数据模型

### tag_rule_relations 表

| 字段 | 类型 | 说明 |
|------|------|------|
| tag_id | Integer | 标签ID（外键） |
| rule_id | Integer | 规则ID（外键） |

### delivery_orders 表新增字段

| 字段 | 类型 | 说明 |
|------|------|------|
| rule_id | Integer | 关联的时间规则ID（可选） |
| rule_item_id | Integer | 关联的时间规则项ID（可选） |

## 技术实现

### 多对多关系

使用SQLAlchemy的关联表实现标签与规则的多对多关系：

```python
tag_rule_relation = Table(
    'tag_rule_relations',
    Base.metadata,
    Column('tag_id', Integer, ForeignKey('tags.id')),
    Column('rule_id', Integer, ForeignKey('rules.id')),
    UniqueConstraint('tag_id', 'rule_id', name='uq_tag_rule')
)
```

### Cron表达式解析

使用croniter库解析Cron表达式并生成时间段：

```python
from croniter import croniter

start_cron = croniter(rule_item.start_time_cron_str, start_date)
next_start = start_cron.get_next(datetime)
```

## 测试

运行集成测试：

```bash
poetry run pytest tests/test_tag_rule_integration.py -v
```

## 相关文档

- [API详细文档](./tag_rule_api_documentation.md)
- [实现总结](./tag_rule_implementation_summary.md)
- [外卖订单API文档](./delivery_order_api_examples.md)

## 注意事项

1. **标签名称唯一性**：标签名称必须唯一，创建前需检查是否已存在
2. **Cron表达式格式**：时间规则的Cron表达式必须符合标准格式
3. **日期范围验证**：生成时间段时，开始日期不能晚于结束日期
4. **可选字段**：外卖订单的rule_id和rule_item_id为可选字段
5. **性能考虑**：对于长时间范围的时间段生成，建议限制日期范围或使用异步处理

## 后续优化

- [ ] 添加缓存机制，提高查询性能
- [ ] 支持时间段的分页查询
- [ ] 添加时间段冲突检测
- [ ] 支持更复杂的时间规则组合
- [ ] 添加订单统计分析功能

## 贡献者

- 开发团队

## 更新日志

### v1.0.0 (2024-01-15)
- ✅ 实现标签与规则的关联功能
- ✅ 实现通过标签获取产品和规则
- ✅ 实现时间段生成功能
- ✅ 实现外卖订单关联时间规则
- ✅ 完成API接口开发
- ✅ 完成数据库迁移
- ✅ 完成集成测试

