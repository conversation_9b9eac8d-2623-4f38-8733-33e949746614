// service/order.js
import { loginRequest } from './index';

/**
 * 获取用户订单列表
 * @param {Object} params 查询参数 {skip: 0, limit: 20}
 * @returns {Promise} 返回订单列表数据
 */
export const getOrderList = async (params = {}) => {
  try {
    const token = wx.getStorageSync('token');
    if (!token) {
      throw new Error('请先登录');
    }

    console.log('发起订单列表请求:', { params, token: token ? '***' : null });

    const response = await loginRequest.get({
      url: '/user/orders',
      data: params,
      header: {
        'token': token
      }
    });
    
    console.log('订单列表API响应:', response);
    return response;
  } catch (error) {
    console.error('获取订单列表失败:', error);
    throw error;
  }
};

/**
 * 获取订单详情
 * @param {number} orderId 订单ID
 * @returns {Promise} 返回订单详情数据
 */
export const getOrderDetail = async (orderId) => {
  try {
    const token = wx.getStorageSync('token');
    if (!token) {
      throw new Error('请先登录');
    }

    const response = await loginRequest.get({
      url: `/user/orders/${orderId}`,
      header: {
        'token': token
      }
    });
    return response;
  } catch (error) {
    console.error('获取订单详情失败:', error);
    throw error;
  }
};

/**
 * 取消订单
 * @param {number} orderId 订单ID
 * @returns {Promise} 返回取消结果
 */
export const cancelOrder = async (orderId) => {
  try {
    const token = wx.getStorageSync('token');
    if (!token) {
      throw new Error('请先登录');
    }

    const response = await loginRequest.post({
      url: `/user/orders/${orderId}/cancel`,
      header: {
        'token': token
      }
    });
    return response;
  } catch (error) {
    console.error('取消订单失败:', error);
    throw error;
  }
};

/**
 * 格式化订单状态显示文本
 * @param {string} status 订单状态
 * @returns {string} 格式化后的状态文本
 */
export const formatOrderStatus = (status) => {
  const statusMap = {
    'pending': '待处理',
    'paid': '已支付',
    'shipped': '已发货',
    'delivered': '已送达',
    'completed': '已完成',
    'cancelled': '已取消',
    'refunded_partial': '部分退',
    'refunded': '已退款'
  };
  return statusMap[status] || status;
};

/**
 * 格式化支付状态显示文本
 * @param {string} paymentStatus 支付状态
 * @returns {string} 格式化后的支付状态文本
 */
export const formatPaymentStatus = (paymentStatus) => {
  const statusMap = {
    'unpaid': '未支付',
    'paid': '已支付',
    'refunded': '已退款'
  };
  return statusMap[paymentStatus] || paymentStatus;
};

/**
 * 格式化订单类型显示文本
 * @param {string} type 订单类型
 * @returns {string} 格式化后的类型文本
 */
export const formatOrderType = (type) => {
  const typeMap = {
    'order': '普通订单',
    'direct_sale': '普通订单',
    'reservation': '预订订单',
    'delivery': '外卖订单',
    'recharge': '充值订单'
  };
  return typeMap[type] || type;
};

/**
 * 格式化支付方式显示文本
 * @param {string} paymentMethod 支付方式
 * @returns {string} 格式化后的支付方式文本
 */
export const formatPaymentMethod = (paymentMethod) => {
  const methodMap = {
    'account_balance': '账户余额',
    'enterprise_account_balance': '企业账户余额',
    'wechat_pay': '微信支付',
    'alipay': '支付宝',
    'cash': '现金'
  };
  return methodMap[paymentMethod] || paymentMethod;
};

/**
 * 判断订单是否可以取消
 * @param {Object} order 订单对象
 * @returns {boolean} 是否可以取消
 */
export const canCancelOrder = (order) => {
  // 只有待处理和已支付状态的订单可以取消
  return order.status === 'pending' || order.status === 'paid';
};

/**
 * 格式化日期时间显示
 * @param {string} dateTime 日期时间字符串
 * @returns {string} 格式化后的日期时间
 */
export const formatDateTime = (dateTime) => {
  if (!dateTime) return '';
  
  try {
    const date = new Date(dateTime);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (error) {
    console.error('日期格式化失败:', error);
    return dateTime;
  }
};
